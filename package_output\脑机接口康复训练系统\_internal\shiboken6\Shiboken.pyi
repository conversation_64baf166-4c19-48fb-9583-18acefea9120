# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only

from __future__ import annotations

"""
This file contains the exact signatures for all functions in module
Shiboken, except for defaults which are replaced by "...".
"""

# Module `Shiboken`

from shiboken6 import Shiboken


class Object(object):

    def __init__(self) -> None: ...


class VoidPtr(object):
    def __init__(self, value: int) -> None: ...


def _unpickle_enum(arg__1: object, arg__2: object) -> object: ...
def createdByPython(arg__1: Shiboken.Object) -> bool: ...
def delete(arg__1: Shiboken.Object) -> None: ...
def dump(arg__1: object) -> str: ...
def getAllValidWrappers() -> list[Shiboken.Object]: ...
def getCppPointer(arg__1: Shiboken.Object) -> tuple[int, ...]: ...
def invalidate(arg__1: Shiboken.Object) -> None: ...
def isValid(arg__1: object) -> bool: ...
def ownedByPython(arg__1: Shiboken.Object) -> bool: ...
def wrapInstance(arg__1: int, arg__2: type) -> Shiboken.Object: ...


# eof
