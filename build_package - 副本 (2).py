#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脑机接口康复训练系统 - 优化打包脚本（保守优化版本）
BCI Rehabilitation Training System - Optimized Build Script (Conservative)

解决所有已知问题：
1. PySide6模块缺失
2. MNE unittest依赖
3. 文件路径问题
4. 实时曲线PyQtGraph支持

优化特性（保守版本）：
1. 排除非中英文语言包
2. 排除开发工具
3. 排除示例数据
4. 优化包大小（预计减少20-35%）
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_dependencies():
    """检查关键依赖"""
    print("检查关键依赖...")
    
    deps = {
        'PySide6': 'PySide6',
        'mne': 'mne',
        'pyqtgraph': 'pyqtgraph',
        'numpy': 'numpy',
        'scipy': 'scipy',
        'matplotlib': 'matplotlib',
        'setuptools': 'setuptools'
    }
    
    missing = []
    for import_name, package_name in deps.items():
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"✓ {package_name}: {version}")
        except ImportError:
            print(f"✗ {package_name}: 缺失")
            missing.append(package_name)
    
    if missing:
        print(f"请安装缺失的包: pip install {' '.join(missing)}")
        return False
    
    return True




def clean_build():
    """清理构建文件"""
    print("清理构建文件...")

    dirs_to_clean = ["dist", "build", "build_optimized", "package_output"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 已清理: {dir_name}")
            except Exception as e:
                print(f"⚠️ 清理失败 {dir_name}: {e}")

    # 清理spec文件
    for spec_file in Path(".").glob("*.spec"):
        try:
            spec_file.unlink()
            print(f"✓ 已清理: {spec_file}")
        except Exception as e:
            print(f"⚠️ 清理spec文件失败 {spec_file}: {e}")


def build_package():
    """构建软件包 - 性能优化版本"""
    print("开始构建优化软件包...")

    cmd = [
        sys.executable, "-O", "-m", "PyInstaller",  # 添加字节码优化
        "--name=脑机接口康复训练系统",
        "--windowed",  # 不显示控制台窗口
        "--onedir",
        "--noconfirm",
        "--clean",
        "--icon=icons/ht.png",  # 设置程序图标
        "--distpath=package_output",
        "--workpath=build",
        "--specpath=.",

        # 优化选项      
        "--log-level=WARN",  # 减少日志输出
       
        
        # 完整收集关键库
        "--collect-all=PySide6",
        "--collect-all=shiboken6",
        "--collect-all=mne",
        # 不使用--collect-all=pyqtgraph，改为手动指定模块避免GUI初始化问题
        "--copy-metadata=PySide6",
        "--copy-metadata=shiboken6",
        "--copy-metadata=mne",
        "--copy-metadata=numpy",
        "--copy-metadata=scipy",
        "--copy-metadata=matplotlib",
        "--copy-metadata=pyqtgraph",
        
        # 数据文件 - 所有文件都放在_internal目录中，避免在exe同级目录创建重复文件
        "--add-data=assets;assets",
        "--add-data=config;config",  # 包含整个config目录
        "--add-data=data/cache;data/cache",  # 仅包含必要的缓存目录结构
        "--add-data=data/logs;data/logs",    # 包含日志目录结构
        "--add-data=data/rehabilitation_raw;data/rehabilitation_raw",  # 康复数据存储目录
        "--add-data=icons;icons",
        "--add-data=libs;libs",  # 添加libs文件夹，包含电刺激DLL
        "--add-data=ShuJu.db;.",
      
        
        # PySide6核心模块
        "--hidden-import=PySide6",
        "--hidden-import=shiboken6",
        "--hidden-import=PySide6.QtCore",
        "--hidden-import=PySide6.QtGui",
        "--hidden-import=PySide6.QtWidgets",
        "--hidden-import=PySide6.QtNetwork",
        "--hidden-import=PySide6.QtSql",
        
        # 科学计算库
        "--hidden-import=numpy",
        "--hidden-import=numpy.core",
        "--hidden-import=numpy.core.multiarray",
        "--hidden-import=numpy.lib",
        "--hidden-import=numpy.linalg",
        "--hidden-import=numpy.fft",
        "--hidden-import=numpy.random",
        
        "--hidden-import=scipy",
        "--hidden-import=scipy.sparse",
        "--hidden-import=scipy.sparse.linalg",
        "--hidden-import=scipy.spatial",
        "--hidden-import=scipy.spatial.distance",
        "--hidden-import=scipy.signal",
        "--hidden-import=scipy.stats",
        "--hidden-import=scipy.linalg",
        
        "--hidden-import=pandas",
        "--hidden-import=pandas.core",
        
        # matplotlib
        "--hidden-import=matplotlib",
        "--hidden-import=matplotlib.pyplot",
        "--hidden-import=matplotlib.figure",
        "--hidden-import=matplotlib.backends",
        "--hidden-import=matplotlib.backends.backend_qt5agg",
        "--hidden-import=matplotlib.backends.backend_agg",
        
        # pyqtgraph - 实时曲线显示关键库（精确指定，避免问题模块）
        "--hidden-import=pyqtgraph",
        "--hidden-import=pyqtgraph.graphicsItems",
        "--hidden-import=pyqtgraph.graphicsItems.PlotItem",
        "--hidden-import=pyqtgraph.graphicsItems.ViewBox",
        "--hidden-import=pyqtgraph.graphicsItems.AxisItem",
        "--hidden-import=pyqtgraph.graphicsItems.PlotCurveItem",
        "--hidden-import=pyqtgraph.graphicsItems.PlotDataItem",
        "--hidden-import=pyqtgraph.graphicsItems.GraphicsObject",
        "--hidden-import=pyqtgraph.graphicsItems.GraphicsWidget",
        "--hidden-import=pyqtgraph.widgets",
        "--hidden-import=pyqtgraph.widgets.PlotWidget",
        "--hidden-import=pyqtgraph.widgets.GraphicsLayoutWidget",
        "--hidden-import=pyqtgraph.Qt",
        "--hidden-import=pyqtgraph.Qt.QtCore",
        "--hidden-import=pyqtgraph.Qt.QtGui",
        "--hidden-import=pyqtgraph.Qt.QtWidgets",
        "--hidden-import=pyqtgraph.functions",
        "--hidden-import=pyqtgraph.Point",
        "--hidden-import=pyqtgraph.Vector",
        "--hidden-import=pyqtgraph.Transform3D",
        "--hidden-import=pyqtgraph.SRTTransform3D",
        "--hidden-import=pyqtgraph.debug",
        "--hidden-import=pyqtgraph.reload",
        "--hidden-import=pyqtgraph.colormap",
        "--hidden-import=pyqtgraph.parametertree",

        # 排除可能导致问题的pyqtgraph模块
        "--exclude-module=pyqtgraph.examples",
        "--exclude-module=pyqtgraph.jupyter",
        "--exclude-module=pyqtgraph.opengl",
        
        # MNE脑电信号处理
        "--hidden-import=mne",
        "--hidden-import=mne.io",
        "--hidden-import=mne.utils",
        "--hidden-import=mne.utils._testing",
        "--hidden-import=mne.preprocessing",
        "--hidden-import=mne.viz",
        "--hidden-import=mne.channels",
        "--hidden-import=mne.filter",
        
        # 标准库 - MNE需要
        "--hidden-import=unittest",
        "--hidden-import=unittest.mock",
        "--hidden-import=collections",
        "--hidden-import=collections.abc",
        "--hidden-import=functools",
        "--hidden-import=itertools",
        "--hidden-import=warnings",
        "--hidden-import=inspect",
        "--hidden-import=pydoc",  # PyQtGraph需要pydoc模块
        
        # 其他库
        "--hidden-import=bleak",
        "--hidden-import=bleak.backends",
        "--hidden-import=bleak.backends.winrt",
        "--hidden-import=PIL",
        "--hidden-import=PIL.Image",
        "--hidden-import=cryptography",
        "--hidden-import=cryptography.fernet",
        "--hidden-import=cryptography.hazmat",
        "--hidden-import=cryptography.hazmat.primitives",
        "--hidden-import=cryptography.hazmat.primitives.hashes",
        "--hidden-import=cryptography.hazmat.backends",
        "--hidden-import=cryptography.hazmat.backends.openssl",
        "--hidden-import=hashlib",  # 硬件指纹哈希保护
        "--hidden-import=time",     # 动态盐值生成
        "--hidden-import=sqlite3",

        # sklearn机器学习库 - 分类器训练必需
        "--hidden-import=sklearn",
        "--hidden-import=sklearn.base",
        "--hidden-import=sklearn.linear_model",
        "--hidden-import=sklearn.svm",
        "--hidden-import=sklearn.ensemble",
        "--hidden-import=sklearn.model_selection",
        "--hidden-import=sklearn.metrics",
        "--hidden-import=sklearn.preprocessing",
        "--hidden-import=sklearn.pipeline",
        "--hidden-import=sklearn.decomposition",
        "--hidden-import=sklearn.discriminant_analysis",

        # 额外的关键科学计算模块
        "--hidden-import=joblib",  # sklearn模型保存
        "--hidden-import=pickle",  # Python序列化
        "--hidden-import=json",    # 配置文件读取
        "--hidden-import=pathlib", # 路径处理

        # setuptools相关依赖 - 修复jaraco.text错误，但排除pkg_resources
        "--hidden-import=setuptools",
        "--exclude-module=setuptools.extern",  # 排除可能有问题的extern模块
        "--hidden-import=jaraco",
        "--hidden-import=jaraco.text",
        "--hidden-import=jaraco.functools",
        "--hidden-import=jaraco.collections",
        "--hidden-import=more_itertools",
        "--copy-metadata=setuptools",
        "--copy-metadata=jaraco.text",
        
        # # 强制包含Python标准库的关键模块
        # "--hidden-import=encodings",
        # "--hidden-import=encodings.utf_8",
        # "--hidden-import=encodings.cp1252",
        # "--hidden-import=codecs",
        
        # 项目模块
        "--hidden-import=app.application",
        "--hidden-import=app.background_loader",
        "--hidden-import=app.config",
        "--hidden-import=core.database",
        "--hidden-import=core.network_config",
        "--hidden-import=core.simple_voice",
        "--hidden-import=core.udp_communicator",
        "--hidden-import=services.auth_service",
        "--hidden-import=services.api_client",
        "--hidden-import=services.patient_service",
        "--hidden-import=services.reference_data_service",
        "--hidden-import=services.treatment_service",
        "--hidden-import=services.user_service",

        # 训练和特征提取核心模块
        "--hidden-import=services.training_session_manager",
        "--hidden-import=services.classifier_training_manager",
        "--hidden-import=services.weighted_voting_classifier",
        "--hidden-import=services.feature_extraction",
        "--hidden-import=services.feature_extraction.base_extractor",
        "--hidden-import=services.feature_extraction.individual_feature_manager",
        "--hidden-import=services.feature_extraction.config",
        "--hidden-import=services.feature_extraction.fbcsp_extractor",
        "--hidden-import=services.feature_extraction.riemannian_extractor",
        "--hidden-import=services.feature_extraction.tef_extractor",
        "--hidden-import=services.feature_extraction.plv_extractor",
        "--hidden-import=services.feature_extraction.tangent_space_extractor",
        "--hidden-import=services.feature_extraction.utils",
        "--hidden-import=services.feature_extraction.utils.signal_utils",
        "--hidden-import=services.feature_extraction.utils.validation_utils",

        # 数据保护和康复评估模块
        "--hidden-import=services.data_protection",
        "--hidden-import=services.data_protection.hardware_fingerprint_protector",

        # 电刺激和硬件控制
        "--hidden-import=services.stimulation",
        "--hidden-import=services.stimulation.stimulation_device",
        "--hidden-import=services.bluetooth",
        "--hidden-import=services.bluetooth.standard_bleak_manager",

        # EEG处理模块
        "--hidden-import=services.eeg_processing",
        "--hidden-import=services.eeg_processing.eeg_data_processor",
        "--hidden-import=services.eeg_preprocessing",
        "--hidden-import=services.eeg_preprocessing.kalman_processor",
        "--hidden-import=services.eeg_preprocessing.preprocessing_config",

        "--hidden-import=ui.login_window",
        "--hidden-import=ui.main_window",
        "--hidden-import=ui.components.modern_card",
        "--hidden-import=ui.components.parameter_adjuster",
        "--hidden-import=ui.components.no_wheel_widgets",
        "--hidden-import=ui.components.mne_topography_widget",
        "--hidden-import=ui.components.pyqtgraph_curves_widget",  # 重要：PyQtGraph曲线组件
        "--hidden-import=ui.themes.theme_manager",
        "--hidden-import=utils.db_helpers",
        "--hidden-import=utils.chart_helpers",
        "--hidden-import=utils.user_helpers",
        
        # 保守优化：排除不必要的模块以减小包大小
        "--exclude-module=tkinter",
        "--exclude-module=doctest",
        "--exclude-module=pydoc_data",
        "--exclude-module=test",
        "--exclude-module=unittest.test",
        "--exclude-module=pip",
        "--exclude-module=wheel",

        # 排除PySide6不需要的模块
        "--exclude-module=PySide6.QtWebEngineWidgets",
        "--exclude-module=PySide6.QtWebEngineCore",
        "--exclude-module=PySide6.QtWebChannel",
        "--exclude-module=PySide6.QtQuick",
        "--exclude-module=PySide6.QtQml",
        "--exclude-module=PySide6.QtMultimedia",
        "--exclude-module=PySide6.QtOpenGL",
        "--exclude-module=PySide6.Qt3DAnimation",
        "--exclude-module=PySide6.Qt3DCore",
        "--exclude-module=PySide6.Qt3DExtras",
        "--exclude-module=PySide6.Qt3DInput",
        "--exclude-module=PySide6.Qt3DLogic",
        "--exclude-module=PySide6.Qt3DRender",

        # 排除测试模块
        "--exclude-module=matplotlib.tests",
        "--exclude-module=numpy.tests",
        "--exclude-module=scipy.tests",
        "--exclude-module=sklearn.tests",
        "--exclude-module=pandas.tests",

        # 排除MNE示例数据和GUI（保守优化）
        "--exclude-module=mne.datasets",
        "--exclude-module=mne.gui",
        "--exclude-module=mne.datasets.sample",
        "--exclude-module=mne.datasets.testing",

        # 排除matplotlib示例数据
        "--exclude-module=matplotlib.mpl-data.sample_data",

        "main.py"
    ]
    
    print("执行构建命令...")
    
    try:
        subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def optimize_package_size():
    """优化打包后的文件大小 - 保守优化版本"""
    print("开始优化包大小...")

    package_dir = Path("package_output/脑机接口康复训练系统/_internal")
    if not package_dir.exists():
        print("⚠️ 打包目录不存在，跳过优化")
        return

    # 统计优化前的大小
    def get_dir_size(path):
        total = 0
        for dirpath, _, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total += os.path.getsize(filepath)
        return total

    original_size = get_dir_size(package_dir)
    print(f"优化前包大小: {original_size / (1024*1024):.1f} MB")

    removed_files = 0
    saved_size = 0

    # 1. 删除非中英文语言包
    translations_dir = package_dir / "PySide6" / "translations"
    if translations_dir.exists():
        print("正在删除非中英文语言包...")
        for file in translations_dir.glob("*.qm"):
            # 保留中文和英文语言包
            if not any(lang in file.name for lang in ['zh_CN', 'zh_TW', 'en']):
                try:
                    file_size = file.stat().st_size
                    file.unlink()
                    removed_files += 1
                    saved_size += file_size
                except Exception as e:
                    print(f"删除文件失败 {file}: {e}")

    # 2. 删除WebEngine本地化文件（除中英文外）
    webengine_locales = package_dir / "PySide6" / "translations" / "qtwebengine_locales"
    if webengine_locales.exists():
        print("正在删除WebEngine非中英文本地化文件...")
        for file in webengine_locales.glob("*.pak"):
            # 保留中文和英文
            if not any(lang in file.name for lang in ['zh-CN', 'zh-TW', 'en-US', 'en-GB']):
                try:
                    file_size = file.stat().st_size
                    file.unlink()
                    removed_files += 1
                    saved_size += file_size
                except Exception as e:
                    print(f"删除文件失败 {file}: {e}")

    # 3. 删除Qt开发工具
    pyside6_dir = package_dir / "PySide6"
    if pyside6_dir.exists():
        print("正在删除Qt开发工具...")
        dev_tools = ['assistant.exe', 'designer.exe', 'linguist.exe', 'lrelease.exe',
                    'lupdate.exe', 'qmlcachegen.exe', 'qmlformat.exe', 'qmlimportscanner.exe',
                    'qmllint.exe', 'qmlls.exe', 'qmltyperegistrar.exe', 'rcc.exe', 'uic.exe']

        for tool in dev_tools:
            tool_path = pyside6_dir / tool
            if tool_path.exists():
                try:
                    file_size = tool_path.stat().st_size
                    tool_path.unlink()
                    removed_files += 1
                    saved_size += file_size
                    print(f"✓ 删除开发工具: {tool}")
                except Exception as e:
                    print(f"删除工具失败 {tool}: {e}")

    # 4. 删除matplotlib示例数据
    mpl_data_dir = package_dir / "matplotlib" / "mpl-data" / "sample_data"
    if mpl_data_dir.exists():
        print("正在删除matplotlib示例数据...")
        try:
            for file in mpl_data_dir.rglob("*"):
                if file.is_file():
                    file_size = file.stat().st_size
                    file.unlink()
                    removed_files += 1
                    saved_size += file_size
            # 删除空目录
            shutil.rmtree(mpl_data_dir)
            print("✓ 删除matplotlib示例数据")
        except Exception as e:
            print(f"删除matplotlib示例数据失败: {e}")

    # 统计优化后的大小
    optimized_size = get_dir_size(package_dir)
    total_saved = original_size - optimized_size

    print("=" * 50)
    print("包大小优化完成！")
    print(f"删除文件数量: {removed_files}")
    print(f"优化前大小: {original_size / (1024*1024):.1f} MB")
    print(f"优化后大小: {optimized_size / (1024*1024):.1f} MB")
    print(f"节省空间: {total_saved / (1024*1024):.1f} MB ({total_saved/original_size*100:.1f}%)")
    print("=" * 50)


def ensure_config_files():
    """确保所有配置文件都被正确复制"""
    print("检查并确保配置文件完整...")

    package_config_dir = Path("package_output/脑机接口康复训练系统/_internal/config")
    source_config_dir = Path("config")

    # 确保目标目录存在
    package_config_dir.mkdir(parents=True, exist_ok=True)

    # 关键配置文件列表
    critical_files = [
        "weighted_voting_config.json",
        "classifiers_optimized.json", 
        "feature_extraction_optimized.json",
        "settings.json",
        "users.json"
    ]

    missing_files = []
    for file_name in critical_files:
        source_file = source_config_dir / file_name
        target_file = package_config_dir / file_name

        if source_file.exists():
            try:
                import shutil
                shutil.copy2(source_file, target_file)
                print(f"✓ 复制配置文件: {file_name}")
            except Exception as e:
                print(f"✗ 复制配置文件失败 {file_name}: {e}")
                missing_files.append(file_name)
        else:
            print(f"⚠️ 源配置文件不存在: {file_name}")
            missing_files.append(file_name)

    if missing_files:
        print(f"⚠️ 缺失的配置文件: {missing_files}")
    else:
        print("✓ 所有关键配置文件都已正确复制")


# def post_process():
#     """后处理 - 创建简单启动脚本"""
#     print("创建启动脚本...")

#     package_dir = Path("package_output/脑机接口康复训练系统")

#     # 创建简单启动脚本
#     startup_script = package_dir / "启动系统.bat"
#     with open(startup_script, 'w', encoding='utf-8') as f:
#         f.write("""@echo off
# chcp 65001 >nul
# echo 正在启动脑机接口康复训练系统...
# echo 请稍候...
# "脑机接口康复训练系统.exe"
# if errorlevel 1 (
#     echo.
#     echo 程序异常退出，按任意键关闭...
#     pause >nul
# )
# """)
    



#     # 确保配置文件完整
#     ensure_config_files()

#     print("✓ 后处理完成")


def main():
    """主函数 - 保守优化打包流程"""
    print("=" * 60)
    print("脑机接口康复训练系统 - 保守优化打包脚本")
    print("BCI Rehabilitation Training System - Conservative Optimized Build")
    print("=" * 60)

    # 检查依赖
    if not check_dependencies():
        return False

    # 清理构建
    clean_build()

    # 构建
    if not build_package():
        return False

    # 优化包大小
    optimize_package_size()

    # 确保配置文件完整
    ensure_config_files()

    # 清理打包工作目录
    print("\n清理打包工作目录...")
    work_dirs_to_clean = ["build", "build_optimized"]
    for dir_name in work_dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 已清理工作目录: {dir_name}")
            except Exception as e:
                print(f"⚠️ 清理工作目录失败 {dir_name}: {e}")

    print("=" * 60)
    print("保守优化打包完成！")
    print("=" * 60)
    print("打包文件位置：package_output/脑机接口康复训练系统/")
    print("主程序：脑机接口康复训练系统.exe")
    print()
    print("保守优化特性：")
    print("✓ 字节码优化 (-O)")
    print("✓ 无控制台窗口 (--windowed)")
    print("✓ 排除非中英文语言包")
    print("✓ 排除开发工具")
    print("✓ 排除示例数据")
    print("✓ 排除测试模块")
    print("✓ 包大小优化（预计减少20-35%）")
    print("✓ 自动清理工作目录")
    print()
    print("已修复的问题：")
    print("✓ PySide6模块缺失")
    print("✓ MNE unittest依赖")
    print("✓ 文件路径问题")
    print("✓ 实时曲线PyQtGraph支持")
    print("✓ 启动速度优化")
    print("✓ 内存使用优化")
    print("✓ 包大小优化")
    print("✓ 数据加密存储功能")
    print("✓ 路径管理器集成")
    print()
    print("可以将整个文件夹复制到其他计算机运行")

    return True


if __name__ == "__main__":
    success = main()
    if not success:
        input("优化打包失败，按回车键退出...")
        sys.exit(1)
    else:
        input("优化打包完成，按回车键退出...")