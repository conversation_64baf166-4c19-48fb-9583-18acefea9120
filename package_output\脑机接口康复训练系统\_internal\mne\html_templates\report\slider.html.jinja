<div class="accordion-item slider {{ klass }}" id="{{ id }}" data-mne-tags="{% for tag in tags %} {{ tag }} {% endfor %}">
  <div class="accordion-header" id="accordion-header-{{ id }}">
    <button class="accordion-button pt-1 pb-1" type="button" data-bs-toggle="collapse"
      data-bs-target="#accordion-collapse-{{ id }}" aria-expanded="true" aria-controls="accordion-collapse-{{ id }}">
      <div class="w-100">
        <span class="me-auto">{{ title }}</span>
        {% for tag in tags %}
        <span class="badge bg-primary rounded-pill float-end me-1" data-mne-tag="{{ tag }}">
          {{ tag }}
        </span>
        {% endfor %}
      </div>
    </button>
  </div>

  <div id="accordion-collapse-{{ id }}" class="accordion-collapse collapse show"
    aria-labelledby="accordion-header-{{ id }}">
    <div class=" accordion-body">
      <div class="mx-auto d-block w-75">
        <label for="slider-{{ id }}" class="form-label small">
          Move slider to change view
        </label>
        <input type="range" class="form-range" min="0" max="{{ images|length - 1 }}" value="{{ start_idx }}"
          id="slider-{{id}}">
      </div>

      <div id="corousel-{{ id }}" class="carousel carousel-dark" data-bs-interval="false" data-bs-wrap="false">
        {# <!-- <div class="carousel-indicators">
          {{for idx, caption in zip(range(len(images)), captions) }}
          <button type="button" data-bs-target="#corousel-{{id}}" data-bs-slide-to="{{idx}}" class="active"
            aria-current="true" aria-label="{{caption}}"></button>
          {{endfor}}
        </div> --> #}

        <div class="carousel-inner">
          {% for idx, img, caption in range(images|length)|zip(images, captions) %}
          <div class="carousel-item {% if idx == start_idx %}active{% endif %}">
            <figure class="figure mx-auto d-block ">
              <img class="figure-img img-fluid rounded mx-auto my-0 d-block" alt="{{title}}"
                src="data:image/{{ image_format }};base64,{{ img }}">
              <figcaption class="figure-caption text-center">
                {{ caption }}
              </figcaption>
            </figure>
          </div>
          {% endfor %}
        </div>

        {# <!-- <button class="carousel-control-prev" type="button" data-bs-target="#corousel-{{id}}" data-bs-slide="prev">
          <span class="carousel-control-prev-icon" aria-hidden="true"></span>
          <span class="visually-hidden">Previous</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#corousel-{{id}}" data-bs-slide="next">
          <span class="carousel-control-next-icon" aria-hidden="true"></span>
          <span class="visually-hidden">Next</span>
        </button> --> #}
      </div>
    </div>
  </div>
</div>
