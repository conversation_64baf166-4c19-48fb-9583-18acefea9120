{"fbcsp_svm": {"type": "SVC", "params": {"C": 0.1, "kernel": "linear", "probability": true, "random_state": 42, "class_weight": "balanced"}, "_comment": "使用更强的正则化，避免小样本过拟合"}, "tef_rf": {"type": "RandomForestClassifier", "params": {"n_estimators": 10, "max_depth": 3, "min_samples_split": 20, "min_samples_leaf": 10, "max_features": "sqrt", "bootstrap": true, "oob_score": true, "random_state": 42, "class_weight": "balanced"}, "_comment": "大幅简化参数防止过拟合：树数量50→10，深度8→3，提高样本要求5→20/2→10"}, "riemannian_meanfield": {"type": "MeanField", "params": {"power_list": [-0.5, 0, 0.5], "method_label": "sum_means", "metric": "logeuclid", "n_jobs": 1}, "fallback": {"type": "SVC", "params": {"C": 0.1, "kernel": "rbf", "gamma": "scale", "probability": true, "random_state": 42}}}, "tangent_space_lr": {"type": "LogisticRegression", "params": {"C": 0.1, "max_iter": 5000, "random_state": 42, "solver": "liblinear", "class_weight": "balanced", "penalty": "l1"}, "_comment": "增强L1正则化：C从1.0→0.1，改用liblinear求解器，移除elasticnet"}, "plv_svm": {"type": "SVC", "params": {"C": 1.0, "kernel": "rbf", "gamma": "auto", "probability": true, "random_state": 42, "class_weight": "balanced"}, "_comment": "PLV特征专用SVM分类器，RBF核适合低SNR环境的非线性模式"}}