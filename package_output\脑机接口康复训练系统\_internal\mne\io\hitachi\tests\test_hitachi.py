# Authors: <AUTHORS>
#
# License: BSD-3-Clause

import datetime as dt

import pytest
import numpy as np
from numpy.testing import assert_allclose, assert_array_less

from mne.channels import make_standard_montage
from mne.io import read_raw_hitachi
from mne.io.hitachi.hitachi import _compute_pairs
from mne.io.tests.test_raw import _test_raw_reader
from mne.preprocessing.nirs import (
    source_detector_distances,
    optical_density,
    tddr,
    beer_lambert_law,
    scalp_coupling_index,
)


CONTENTS = dict()
CONTENTS[
    "1.18"
] = b"""\
Header,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
File Version,1.18,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Patient Information,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
ID,TestID,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Name,Test,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Comment,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Age, 45y,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Sex,Female,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Analyze Information,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
AnalyzeMode,Continuous,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Pre Time[s],9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Post Time[s],7,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Recovery Time[s],12,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Base Time[s],10,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Fitting Degree,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
HPF[Hz],No Filter,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
LPF[Hz],0.1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Moving Average[s],0.1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Measure Information,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Date,5/17/04 5:14,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Mode,3x5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Wave[nm],695,830,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Wave Length,CH1(700.1),CH1(829.1),CH2(699.3),CH2(827.9),CH3(699.3),CH3(827.9),CH4(698.6),CH4(828.1),CH5(700.1),CH5(829.1),CH6(698.5),CH6(828.2),CH7(699.3),CH7(827.9),CH8(699.8),CH8(828.5),CH9(698.6),CH9(828.1),CH10(698.5),CH10(828.2),CH11(698.5),CH11(828.2),CH12(699.8),CH12(828.5),CH13(699.8),CH13(828.5),CH14(699.0),CH14(828.2),CH15(698.5),CH15(828.2),CH16(699.5),CH16(828.1),CH17(699.8),CH17(828.5),CH18(699.5),CH18(828.5),CH19(699.0),CH19(828.2),CH20(699.5),CH20(828.1),CH21(699.5),CH21(828.1),CH22(699.5),CH22(828.5),,,,,
Analog Gain,30.117647,30.117647,30.117647,30.117647,94.117647,94.117647,94.117647,94.117647,10.27451,10.27451,30.117647,30.117647,59.607843,59.607843,94.117647,94.117647,110.588235,110.588235,10.27451,10.27451,59.607843,59.607843,59.607843,59.607843,110.588235,110.588235,10.27451,10.27451,41.176471,41.176471,59.607843,59.607843,9.333333,9.333333,110.588235,110.588235,41.176471,41.176471,41.176471,41.176471,9.333333,9.333333,9.333333,9.333333,,,,,
Digital Gain,13.38,2.82,44.57,7.52,29.46,4.12,36.56,4.84,6.67,1.37,22.61,3.28,79.19,10.88,16.37,2.78,39.97,4.09,100,38.02,36.58,3.95,61.77,7.67,100,15.23,31.52,7.15,53.46,5.05,13.61,2.07,63.74,11.16,14.35,2.05,58.64,8.93,14.53,1.99,8.16,1.82,27.84,5.97,,,,,
Sampling Period[s],0.1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
StimType,BLOCK,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Stim Time[s],20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
F1,15,F2,15,F3,15,F4,15,F5,15,F6,15,F7,15,F8,15,F9,15,M,15,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Repeat Count,3,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Exception Ch,0,0,0,0,0,0,1,0,0,1,0,0,1,0,0,0,0,0,0,0,0,0,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Data,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
Probe1,CH1(700.1),CH1(829.1),CH2(699.3),CH2(827.9),CH3(699.3),CH3(827.9),CH4(698.6),CH4(828.1),CH5(700.1),CH5(829.1),CH6(698.5),CH6(828.2),CH7(699.3),CH7(827.9),CH8(699.8),CH8(828.5),CH9(698.6),CH9(828.1),CH10(698.5),CH10(828.2),CH11(698.5),CH11(828.2),CH12(699.8),CH12(828.5),CH13(699.8),CH13(828.5),CH14(699.0),CH14(828.2),CH15(698.5),CH15(828.2),CH16(699.5),CH16(828.1),CH17(699.8),CH17(828.5),CH18(699.5),CH18(828.5),CH19(699.0),CH19(828.2),CH20(699.5),CH20(828.1),CH21(699.5),CH21(828.1),CH22(699.5),CH22(828.5),Mark,Time,BodyMovement,RemovalMark,PreScan
1,1.99371338,1.95037842,2.24243164,2.17483521,2.2052002,2.16384888,2.18017578,2.10418701,1.75735474,1.63879395,1.99432373,1.88644409,2.1307373,2.05368042,2.08358765,1.96212769,1.89682007,1.97311401,0.6993103,1.8006897,2.01049805,1.86462402,1.95968628,1.87988281,1.59408569,1.97891235,1.9392395,1.73034668,2.18307495,2.00424194,1.90841675,1.87286377,1.89331055,1.79077148,1.82418823,1.82601929,2.44125366,2.05291748,2.09381104,2.03796387,1.92672729,1.90353394,2.10266113,2.0401001,0,14:08.2,0,0,1
2,1.97433472,1.94091797,2.31689453,2.16079712,2.22290039,2.15209961,2.12936401,2.08328247,1.75445557,1.63269043,2.01126099,1.88186646,2.11425781,2.04345703,2.07015991,1.95236206,1.90826416,1.95266724,0.71853638,1.78970337,2.03872681,1.8572998,1.95602417,1.86798096,1.59118652,1.94122314,1.93054199,1.72439575,2.21435547,2.00531006,1.90200806,1.86508179,1.87957764,1.78085327,1.81564331,1.81365967,2.33200073,2.02987671,2.0980835,2.02682495,1.92520142,1.8939209,2.10327148,2.01843262,0,14:08.4,0,0,1
3,1.97616577,1.93359375,2.2819519,2.15866089,2.19512939,2.15301514,2.10266113,2.07931519,1.74468994,1.62582397,2.04330444,1.87774658,2.06222534,2.0425415,2.06008911,1.95098877,1.94946289,1.94793701,0.69549561,1.76803589,1.9909668,1.85028076,1.93374634,1.86599731,1.60140991,1.92367554,1.92260742,1.72225952,2.16796875,1.99523926,1.89208984,1.86080933,1.88095093,1.78207397,1.80541992,1.81091309,2.32818604,2.01675415,2.10418701,2.01965332,1.92001343,1.88995361,2.08648682,2.01065063,0,14:08.4,0,0,1
4,1.97799683,1.93695068,2.25875854,2.16461182,2.23007202,2.15774536,2.12554932,2.08068848,1.73980713,1.62155151,2.02072144,1.87957764,2.09320068,2.0401001,2.06558228,1.95327759,1.90414429,1.953125,0.66207886,1.77108765,2.01797485,1.85043335,1.98562622,1.86843872,1.48071289,1.93771362,1.92520142,1.71554565,2.18002319,1.98654175,1.89208984,1.85958862,1.88766479,1.78253174,1.81396484,1.80923462,2.34085083,2.01660156,2.09869385,2.0211792,1.92108154,1.89041138,2.07946777,2.01278687,0,14:08.6,0,0,1
5,1.97219849,1.93847656,2.315979,2.16430664,2.20230103,2.15713501,2.07458496,2.07824707,1.73614502,1.62399292,2.00408936,1.87652588,2.1005249,2.03887939,2.07061768,1.95220947,1.89361572,1.95129395,0.72784424,1.79977417,2.01309204,1.85089111,1.88674927,1.86035156,1.40136719,1.97387695,1.91711426,1.71966553,2.16247559,1.99234009,1.8737793,1.86035156,1.8989563,1.78543091,1.83700562,1.80877686,2.39151001,2.02896118,2.08709717,2.02224731,1.92138672,1.89025879,2.09014893,2.01431274,0,14:08.6,0,0,1
6,1.96807861,1.93969727,2.27905273,2.15988159,2.22793579,2.15789795,2.10266113,2.08618164,1.73950195,1.62445068,2.01156616,1.87728882,2.08847046,2.03811646,2.06420898,1.95617676,1.91879272,1.95541382,0.7019043,1.77963257,1.98974609,1.85195923,1.93954468,1.86630249,1.57897949,2.00317383,1.91040039,1.72515869,2.24868774,1.99157715,1.89849854,1.86218262,1.90658569,1.78634644,1.82495117,1.81427002,2.45651245,2.03353882,2.10083008,2.02758789,1.92184448,1.89331055,2.09686279,2.01980591,0,14:08.8,0,0,1
7,1.98776245,1.94198608,2.2895813,2.15759277,2.21405029,2.16812134,2.15332031,2.09991455,1.74499512,1.62765503,2.01919556,1.87911987,2.2026062,2.04589844,2.05276489,1.96105957,1.93862915,1.96105957,0.67474365,1.78604126,1.99249268,1.84967041,1.89849854,1.86676025,1.39373779,1.95846558,1.90979004,1.72698975,2.27798462,1.99798584,1.91055298,1.86172485,1.91375732,1.78375244,1.78024292,1.8157959,2.30819702,2.05245972,2.1043396,2.03079224,1.925354,1.89697266,2.11288452,2.02545166,0,14:08.9,0,0,1
8,1.98364258,1.94488525,2.28744507,2.16598511,2.20123291,2.17254639,2.13745117,2.10357666,1.75003052,1.63162231,2.00790405,1.87637329,2.20916748,2.05444336,2.05459595,1.95983887,1.90536499,1.96777344,0.72494507,1.78741455,1.98318481,1.85516357,1.89605713,1.86798096,1.4453125,1.95663452,1.91436768,1.73355103,2.22427368,2.00180054,1.89605713,1.86599731,1.86950684,1.79077148,1.79962158,1.82327271,2.31536865,2.06726074,2.09564209,2.03521729,1.92459106,1.90078735,2.10281372,2.03353882,0,14:08.9,0,0,1
9,1.97967529,1.95297241,2.34939575,2.18048096,2.22961426,2.1711731,2.10784912,2.10617065,1.75308228,1.6368103,2.00836182,1.87728882,2.07595825,2.04437256,2.04742432,1.96411133,1.92672729,1.97036743,0.65322876,1.78192139,2.00515747,1.85562134,1.99584961,1.86828613,1.41708374,1.91955566,1.92321777,1.73690796,2.25341797,1.99813843,1.89788818,1.86904907,1.8699646,1.79336548,1.80130005,1.82952881,2.44827271,2.08602905,2.10388184,2.03964233,1.9291687,1.90505981,2.0980835,2.0401001,0,14:09.1,0,0,1
10,1.96777344,1.95663452,2.3046875,2.17712402,2.26348877,2.17590332,2.14691162,2.11624146,1.75506592,1.64108276,2.01538086,1.88278198,2.21374512,2.04696655,2.07824707,1.96838379,1.94854736,1.97387695,0.64300537,1.80709839,2.03277588,1.86340332,1.96380615,1.87942505,1.38870239,1.95419312,1.92611694,1.74087524,2.30636597,2.00012207,1.90124512,1.87103271,1.88766479,1.79397583,1.81411743,1.83273315,2.38845825,2.09106445,2.12188721,2.04544067,1.93237305,1.90994263,2.12310791,2.04696655,0,14:09.1,0,0,1
11,1.98181152,1.96182251,2.2883606,2.1812439,2.25265503,2.18231201,2.12814331,2.11181641,1.75842285,1.64596558,2.0135498,1.88995361,2.15209961,2.05230713,2.07077026,1.97387695,1.86767578,1.98471069,0.69610596,1.80511475,1.9960022,1.87103271,1.90963745,1.8838501,1.41983032,1.98654175,1.91070557,1.74255371,2.22869873,2.00119019,1.8977356,1.87103271,1.87271118,1.80007935,1.83288574,1.83670044,2.45758057,2.09213257,2.08786011,2.04910278,1.93267822,1.90963745,2.10998535,2.05307007,0,14:09.2,0,0,1
12,1.9909668,1.95526123,2.2756958,2.17941284,2.26486206,2.17056274,2.14797974,2.09945679,1.75415039,1.64199829,2.02407837,1.8913269,2.07015991,2.05383301,2.05596924,1.96411133,1.85272217,1.96365356,0.62423706,1.79443359,1.99417114,1.86538696,1.96487427,1.87637329,1.50161743,1.97616577,1.92626953,1.73583984,2.29568481,2.00210571,1.90765381,1.86904907,1.87347412,1.79473877,1.81488037,1.82327271,2.35809326,2.07244873,2.09411621,2.03353882,1.92932129,1.90338135,2.10510254,2.03475952,0,14:09.4,0,0,1
13,1.98287964,1.94839478,2.24395752,2.18048096,2.22961426,2.16156006,2.13409424,2.08709717,1.74362183,1.62902832,2.0111084,1.89331055,2.10952759,2.05001831,2.06558228,1.95846558,1.92581177,1.95098877,0.67565918,1.77017212,1.96060181,1.85882568,1.95159912,1.8762207,1.68884277,1.97921753,1.92855835,1.73416138,2.23876953,2.00210571,1.90200806,1.86828613,1.88858032,1.78710938,1.83029175,1.81137085,2.3526001,2.04116821,2.10769653,2.02423096,1.92565918,1.90002441,2.09457397,2.01904297,0,14:09.5,0,0,1
14,1.99539185,1.93984985,2.27966309,2.16644287,2.20947266,2.16186523,2.12966919,2.08984375,1.73339844,1.61865234,2.02865601,1.89239502,2.11593628,2.05337524,2.05307007,1.95632935,1.9682312,1.95175171,0.69198608,1.7729187,1.95541382,1.85348511,1.95953369,1.86935425,1.55059814,1.9203186,1.91192627,1.73065186,2.30255127,1.99172974,1.89743042,1.86325073,1.87255859,1.7855835,1.83547974,1.81137085,2.35107422,2.0413208,2.09243774,2.01477051,1.92810059,1.89575195,2.10174561,2.01538086,0,14:09.6,0,0,1
15,1.97799683,1.9380188,2.25616455,2.15927124,2.23983765,2.16186523,2.15667725,2.0942688,1.72332764,1.61178589,2.04833984,1.88995361,2.12112427,2.04421997,2.0703125,1.9569397,1.98425293,1.9581604,0.6690979,1.75369263,1.97433472,1.8536377,1.90307617,1.87408447,1.5020752,1.92214966,1.90536499,1.7276001,2.22579956,1.98196411,1.90704346,1.86431885,1.88552856,1.78817749,1.8157959,1.81396484,2.34634399,2.02667236,2.05535889,2.01370239,1.92642212,1.8963623,2.10540771,2.01812744,0,14:09.6,0,0,1
16,1.97402954,1.94320679,2.28927612,2.16308594,2.22045898,2.15759277,2.13287354,2.09533691,1.72103882,1.61392212,2.03170776,1.89239502,2.12036133,2.0539856,2.05886841,1.95785522,1.96426392,1.95388794,0.64727783,1.75918579,2.00241089,1.85623169,1.95159912,1.86813354,1.42822266,1.94442749,1.91574097,1.73110962,2.2265625,1.98608398,1.90155029,1.86447144,1.87133789,1.7880249,1.82418823,1.81488037,2.38815308,2.03323364,2.06680298,2.01141357,1.92764282,1.89865112,2.11410522,2.02316284,0,14:09.8,0,0,1
17,1.97616577,1.94488525,2.28408813,2.16262817,2.22442627,2.16323853,2.10845947,2.09335327,1.72180176,1.61437988,2.01812744,1.89025879,2.14065552,2.04421997,2.05230713,1.95846558,1.93649292,1.95465088,0.62026978,1.77139282,1.99203491,1.85516357,1.91833496,1.8598938,1.5234375,1.96426392,1.92260742,1.73278809,2.18643188,1.98486328,1.91436768,1.86691284,1.87942505,1.79458618,1.82723999,1.82144165,2.24609375,2.04086304,2.08587646,2.01477051,1.92611694,1.90170288,2.10418701,2.03155518,0,14:09.8,0,0,1
18,1.98242188,1.94839478,2.23770142,2.17681885,2.24349976,2.16690063,2.14187622,2.10189819,1.72317505,1.6166687,2.01126099,1.89300537,2.2442627,2.05917358,2.07855225,1.95892334,2.00881958,1.96578979,0.66619873,1.79229736,2.00164795,1.85974121,1.94900513,1.86813354,1.49673462,1.97021484,1.93161011,1.73477173,2.26104736,1.99493408,1.9052124,1.86889648,1.85714722,1.79290771,1.80648804,1.82571411,2.33688354,2.03872681,2.0715332,2.02072144,1.92749023,1.9039917,2.11380005,2.03704834,0,14:09.9,0,0,1
19,1.99203491,1.95053101,2.21984863,2.16567993,2.24914551,2.16644287,2.17529297,2.11242676,1.72943115,1.62200928,2.01705933,1.89071655,2.08297729,2.05413818,2.06710815,1.96289063,1.95388794,1.97113037,0.65765381,1.79656982,1.99569702,1.86813354,1.95297241,1.88034058,1.61651611,1.96380615,1.91329956,1.7388916,2.31140137,1.99768066,1.91375732,1.87347412,1.88522339,1.80053711,1.8321228,1.83013916,2.43652344,2.05963135,2.0715332,2.02224731,1.93084717,1.9078064,2.11685181,2.04299927,0,14:10.1,0,0,1
20,1.99111938,1.95373535,2.26196289,2.18078613,2.1987915,2.16827393,2.13729858,2.11776733,1.73477173,1.6267395,2.01141357,1.88583374,2.09472656,2.05413818,2.0602417,1.97067261,1.92123413,1.97341919,0.67626953,1.81976318,1.98623657,1.8711853,1.99081421,1.88323975,1.51351929,1.94259644,1.91070557,1.74301147,2.34313965,1.99783325,1.90933228,1.87561035,1.91986084,1.80526733,1.83883667,1.83425903,2.3789978,2.07061768,2.10906982,2.02911377,1.93069458,1.91040039,2.11120605,2.05444336,0,14:10.1,0,0,1
21,1.99432373,1.95632935,2.26348877,2.17254639,2.22091675,2.17041016,2.1395874,2.11257935,1.73446655,1.62948608,2.0085144,1.88919067,2.13272095,2.06359863,2.07229614,1.96777344,1.91635132,1.97753906,0.67993164,1.78771973,2.01278687,1.87179565,1.92855835,1.88446045,1.44363403,1.96456909,1.90948486,1.74346924,2.24441528,2.0022583,1.90689087,1.87637329,1.91253662,1.80480957,1.81213379,1.83670044,2.32421875,2.07244873,2.08938599,2.03018188,1.93405151,1.91101074,2.12799072,2.05245972,0,14:10.3,0,0,1
22,1.97540283,1.94702148,2.29507446,2.16796875,2.23678589,2.16369629,2.15377808,2.09854126,1.73431396,1.62582397,2.02285767,1.88613892,2.14736938,2.05108643,2.06466675,1.95739746,1.98852539,1.95831299,0.66726685,1.80206299,1.99890137,1.87103271,1.95175171,1.87332153,1.48300171,1.93161011,1.91070557,1.73553467,2.3236084,1.99890137,1.89224243,1.87210083,1.90490723,1.79199219,1.80984497,1.82266235,2.44430542,2.059021,2.09091187,2.02682495,1.92596436,1.90322876,2.11151123,2.0300293,0,14:10.3,0,0,1
23,1.98196411,1.94076538,2.25891113,2.17376709,2.22000122,2.162323,2.11456299,2.07885742,1.73431396,1.62353516,2.02423096,1.88400269,2.14614868,2.0489502,2.04788208,1.95053101,1.9392395,1.93847656,0.6968689,1.80007935,1.99325562,1.86325073,1.92306519,1.86325073,1.52069092,1.96899414,1.89575195,1.73110962,2.38296509,1.99050903,1.90322876,1.86798096,1.88522339,1.79397583,1.81793213,1.81259155,2.34802246,2.05993652,2.09609985,2.02072144,1.92993164,1.89910889,2.09411621,2.01675415,0,14:10.5,0,0,1
24,1.97601318,1.93710327,2.27630615,2.16629028,2.22366333,2.15438843,2.13867188,2.08312988,1.73614502,1.62216187,2.00546265,1.88217163,2.11608887,2.0501709,2.04437256,1.94610596,1.88034058,1.93847656,0.68283081,1.78573608,1.99447632,1.86126709,1.93969727,1.86203003,1.6204834,1.95159912,1.90353394,1.72515869,2.29537964,1.99157715,1.90353394,1.86798096,1.90856934,1.78466797,1.77825928,1.81182861,2.35046387,2.06130981,2.10220337,2.02468872,1.92657471,1.89956665,2.10189819,2.01812744,0,14:10.6,0,0,1
25,1.97372437,1.9380188,2.26913452,2.16491699,2.20046997,2.15499878,2.13699341,2.0916748,1.73629761,1.62216187,2.02514648,1.88156128,2.17895508,2.03781128,2.06069946,1.94992065,1.95129395,1.9493103,0.65811157,1.78390503,2.00195313,1.85958862,1.96380615,1.86416626,1.69540405,1.92749023,1.91375732,1.72927856,2.30285645,1.98608398,1.90231323,1.86950684,1.90658569,1.79290771,1.80221558,1.81259155,2.39135742,2.05551147,2.09152222,2.02423096,1.92901611,1.90032959,2.10449219,2.02301025,0,14:10.6,0,0,1
26,1.98699951,1.9418335,2.2845459,2.16461182,2.21847534,2.15362549,2.14553833,2.0916748,1.73446655,1.62445068,2.02957153,1.88095093,2.18887329,2.05001831,2.05780029,1.94961548,1.97113037,1.95617676,0.6690979,1.77810669,1.98165894,1.86172485,1.93984985,1.86752319,1.46759033,1.93847656,1.90689087,1.73477173,2.2215271,1.99356079,1.9078064,1.86767578,1.87561035,1.79550171,1.78695679,1.8145752,2.38891602,2.06634521,2.0803833,2.02377319,1.93099976,1.8989563,2.11105347,2.02850342,0,14:10.8,0,0,1
27,1.99172974,1.94488525,2.28012085,2.16674805,2.24365234,2.15255737,2.14172363,2.1055603,1.7350769,1.62643433,2.02728271,1.88201904,2.13470459,2.03796387,2.06558228,1.95373535,1.9128418,1.9631958,0.67138672,1.78161621,1.99951172,1.86965942,1.90719604,1.87133789,1.49520874,1.96914673,1.90155029,1.73812866,2.16400146,2.00546265,1.89971924,1.86859131,1.89453125,1.79473877,1.82540894,1.81884766,2.40783691,2.07489014,2.08694458,2.02423096,1.92840576,1.90185547,2.10632324,2.03353882,0,14:10.8,0,0,1
28,1.9732666,1.95175171,2.25753784,2.16812134,2.240448,2.16156006,2.11791992,2.10235596,1.73721313,1.63009644,1.99203491,1.88873291,2.20687866,2.04162598,2.0489502,1.95770264,1.93862915,1.95953369,0.67749023,1.79397583,2.0211792,1.86920166,1.85409546,1.87423706,1.53579712,1.94671631,1.90414429,1.74194336,2.23876953,1.99981689,1.91268921,1.86859131,1.88369751,1.79840088,1.83441162,1.82449341,2.38571167,2.07824707,2.09381104,2.02697754,1.92687988,1.90490723,2.10479736,2.04147339,0,14:11.0,0,0,1
29,1.96792603,1.95770264,2.28240967,2.16888428,2.22106934,2.16705322,2.1295166,2.10174561,1.74163818,1.63330078,1.99935913,1.8901062,2.05825806,2.04925537,2.0401001,1.95861816,1.91741943,1.96105957,0.70831299,1.81396484,1.99874878,1.86752319,1.96304321,1.87301636,1.42089844,1.96243286,1.90505981,1.74209595,2.13973999,2.00485229,1.90628052,1.87057495,1.88659668,1.80053711,1.83898926,1.82998657,2.44888306,2.07855225,2.1055603,2.02880859,1.93206787,1.90536499,2.11380005,2.04437256,0,14:11.1,0,0,1
30,1.97982788,1.95861816,2.2505188,2.16903687,2.2227478,2.16842651,2.12631226,2.11807251,1.74026489,1.63619995,1.99783325,1.89453125,2.05108643,2.04086304,2.06497192,1.96716309,1.87042236,1.97525024,0.67626953,1.82174683,2.00271606,1.87316895,1.97113037,1.87850952,1.46469116,1.95175171,1.92520142,1.74423218,2.26837158,1.99874878,1.9090271,1.87347412,1.90673828,1.80084229,1.83151245,1.82739258,2.51907349,2.09030151,2.10220337,2.03170776,1.93618774,1.9102478,2.11425781,2.05169678,0,14:11.2,0,0,1
31,1.97662354,1.96273804,2.19848633,2.17575073,2.19696045,2.16384888,2.14111328,2.12295532,1.74194336,1.63970947,2.01950073,1.89575195,2.13088989,2.06619263,2.04589844,1.96792603,1.90551758,1.98348999,0.65628052,1.81152344,2.01904297,1.87957764,1.92276001,1.88323975,1.38900757,1.93771362,1.90887451,1.75201416,2.27508545,2.00576782,1.91711426,1.87973022,1.88964844,1.80877686,1.82907104,1.83349609,2.44598389,2.09075928,2.10540771,2.0401001,1.93710327,1.91833496,2.11593628,2.05657959,0,14:11.2,0,0,1
32,1.98669434,1.96029663,2.29675293,2.18917847,2.212677,2.16842651,2.14202881,2.11395264,1.74453735,1.64199829,2.00759888,1.89605713,2.14096069,2.05078125,2.0463562,1.96517944,2.01156616,1.97677612,0.64559937,1.80419922,1.98120117,1.88049316,1.91513062,1.88598633,1.56158447,1.95755005,1.91253662,1.75048828,2.18658447,2.00912476,1.90185547,1.88064575,1.88568115,1.80496216,1.8296814,1.83319092,2.41394043,2.10067749,2.10159302,2.0324707,1.9354248,1.9140625,2.11898804,2.05200195,0,14:11.3,0,0,1
33,1.98532104,1.953125,2.27600098,2.18566895,2.20947266,2.15698242,2.13500977,2.0954895,1.74285889,1.63894653,2.01202393,1.88949585,2.19451904,2.06787109,2.05490112,1.95678711,1.97540283,1.96029663,0.66055298,1.81503296,1.97738647,1.87210083,1.97067261,1.87454224,1.6456604,1.96868896,1.90658569,1.74728394,2.25372314,2.01553345,1.90567017,1.87515259,1.90261841,1.79962158,1.82418823,1.81686401,2.33856201,2.09381104,2.09152222,2.02682495,1.93237305,1.90582275,2.10144043,2.03186035,0,14:11.5,0,0,1
34,1.98028564,1.94595337,2.23480225,2.17849731,2.20993042,2.15255737,2.09884644,2.08557129,1.74606323,1.63528442,2.02789307,1.88995361,2.24090576,2.05276489,2.06481934,1.94869995,1.96670532,1.93939209,0.71411133,1.82739258,2.00210571,1.87133789,1.8951416,1.87179565,1.40487671,1.97174072,1.92230225,1.73446655,2.30804443,1.99539185,1.90155029,1.87393188,1.90307617,1.79489136,1.84265137,1.81015015,2.44552612,2.05978394,2.08709717,2.02377319,1.927948,1.902771,2.09716797,2.02133179,0,14:11.6,0,0,1
35,1.97540283,1.9418335,2.27020264,2.17758179,2.19360352,2.15652466,2.16308594,2.08129883,1.74377441,1.63543701,2.00958252,1.88842773,2.11303711,2.06390381,2.06314087,1.9493103,1.9619751,1.93954468,0.69076538,1.81472778,2.03689575,1.86843872,1.91299438,1.87408447,1.51916504,1.91619873,1.91848755,1.73812866,2.23510742,1.98944092,1.90612793,1.87515259,1.91253662,1.78939819,1.82876587,1.81427002,2.47390747,2.06634521,2.10739136,2.02651978,1.92840576,1.90383911,2.09747314,2.02224731,0,14:11.7,0,0,1
36,1.98654175,1.94610596,2.27264404,2.17437744,2.22106934,2.15866089,2.12554932,2.08435059,1.74606323,1.63543701,1.9984436,1.88232422,2.15118408,2.07473755,2.06695557,1.94976807,1.91421509,1.94625854,0.6652832,1.7930603,2.01431274,1.8661499,1.9052124,1.86920166,1.55166626,1.90185547,1.90719604,1.74057007,2.18048096,1.99295044,1.90582275,1.87240601,1.87057495,1.79382324,1.81945801,1.81213379,2.37976074,2.06634521,2.12615967,2.02682495,1.92977905,1.90292358,2.10525513,2.02194214,0,14:11.8,0,0,1
37,1.97418213,1.95037842,2.25952148,2.17269897,2.22961426,2.15362549,2.0614624,2.09396362,1.74377441,1.63726807,1.98318481,1.88934326,2.14096069,2.05230713,2.06344604,1.953125,1.89468384,1.9468689,0.64910889,1.81091309,1.97463989,1.86981201,1.90811157,1.87866211,1.52664185,1.94763184,1.89559937,1.73934937,2.22137451,2.00042725,1.90795898,1.87728882,1.88369751,1.79885864,1.82128906,1.80984497,2.44415283,2.081604,2.10021973,2.02728271,1.93084717,1.90689087,2.11395264,2.02575684,0,14:11.8,0,0,1
38,1.97113037,1.95098877,2.28057861,2.17910767,2.20901489,2.1546936,2.10067749,2.09320068,1.74591064,1.63955688,1.97174072,1.89056396,2.1446228,2.04574585,2.05657959,1.95877075,1.92642212,1.95083618,0.63217163,1.81213379,2.00714111,1.87301636,1.98638916,1.87606812,1.45858765,1.99295044,1.90216064,1.74407959,2.17819214,1.99295044,1.92062378,1.87606812,1.88461304,1.80175781,1.82876587,1.81533813,2.4307251,2.07778931,2.09899902,2.02865601,1.92947388,1.90719604,2.11364746,2.03216553,0,14:11.9,0,0,1
39,1.98394775,1.95678711,2.25265503,2.18460083,2.21923828,2.15988159,2.15713501,2.09335327,1.74743652,1.64108276,2.04437256,1.89544678,2.12524414,2.05551147,2.05307007,1.96258545,1.99295044,1.95983887,0.64346313,1.82022095,2.00897217,1.87362671,1.93954468,1.87927246,1.52832031,1.95571899,1.9203186,1.75460815,2.2946167,2.006073,1.91299438,1.87805176,1.88018799,1.80877686,1.82983398,1.822052,2.30957031,2.07626343,2.09136963,2.03262329,1.93527222,1.91085815,2.12005615,2.04284668,0,14:12.1,0,0,1
40,1.99996948,1.95755005,2.28988647,2.18170166,2.22320557,2.17285156,2.16186523,2.10845947,1.74118042,1.63909912,2.05337524,1.90185547,2.13424683,2.06466675,2.07107544,1.96563721,1.96090698,1.96670532,0.71044922,1.79550171,2.01095581,1.8762207,1.97921753,1.8901062,1.50222778,1.93222046,1.92138672,1.75964355,2.3059082,2.01477051,1.91619873,1.87973022,1.90139771,1.80679321,1.83731079,1.82510376,2.34085083,2.08114624,2.08953857,2.02774048,1.93740845,1.91421509,2.11639404,2.04940796,0,14:12.2,0,0,1
41,1.98394775,1.95785522,2.25570679,2.17987061,2.19512939,2.17559814,2.11608887,2.11883545,1.7388916,1.64154053,2.0324707,1.90444946,2.1333313,2.06680298,2.06741333,1.97052002,1.94564819,1.97097778,0.71166992,1.81335449,1.99691772,1.87667847,1.94152832,1.8901062,1.54220581,1.95541382,1.91833496,1.7666626,2.22686768,2.00790405,1.92260742,1.87988281,1.88781738,1.81121826,1.83670044,1.83380127,2.39196777,2.09289551,2.10189819,2.02896118,1.9380188,1.91558838,2.1182251,2.05627441,0,14:12.2,0,0,1
42,1.96655273,1.96670532,2.2428894,2.18521118,2.23602295,2.1824646,2.16140747,2.11669922,1.74179077,1.64825439,2.03186035,1.90765381,2.07855225,2.06985474,2.05505371,1.97662354,1.98059082,1.97189331,0.6741333,1.84951782,1.97174072,1.88201904,1.95968628,1.89102173,1.41387939,1.98577881,1.92245483,1.77017212,2.16629028,2.0199585,1.9229126,1.88522339,1.87713623,1.81396484,1.81137085,1.8371582,2.3600769,2.11273193,2.07809448,2.03613281,1.94168091,1.92138672,2.12341309,2.06573486,0,14:12.4,0,0,1
43,1.97341919,1.96578979,2.24502563,2.19055176,2.23724365,2.17605591,2.15484619,2.12188721,1.746521,1.6494751,2.01507568,1.90429688,2.10388184,2.06283569,2.07687378,1.97143555,1.96487427,1.97555542,0.68862915,1.82952881,2.03384399,1.88674927,1.96395874,1.8850708,1.54769897,1.99737549,1.92306519,1.76605225,2.21679688,2.0211792,1.91802979,1.88659668,1.87820435,1.81503296,1.82281494,1.84005737,2.43560791,2.10571289,2.10769653,2.03689575,1.94198608,1.91970825,2.11730957,2.06207275,0,14:12.5,0,0,1
44,1.97677612,1.95495605,2.31628418,2.1824646,2.24975586,2.16705322,2.16384888,2.10510254,1.74972534,1.64962769,2.00683594,1.89987183,2.18933105,2.05993652,2.05795288,1.96350098,1.96136475,1.95556641,0.67184448,1.83441162,2.01568604,1.88369751,1.93725586,1.88537598,1.53778076,1.9644165,1.9078064,1.76193237,2.23556519,2.01507568,1.92657471,1.88308716,1.90872192,1.80892944,1.80892944,1.824646,2.41073608,2.10205078,2.10174561,2.02835083,1.93664551,1.91375732,2.10754395,2.04376221,0,14:12.6,0,0,1
45,1.98364258,1.95449829,2.40234375,2.18063354,2.24273682,2.16293335,2.12936401,2.08892822,1.75170898,1.6468811,2.03491211,1.89651489,2.09945679,2.06298828,2.04452515,1.9619751,1.95175171,1.94580078,0.67153931,1.84738159,1.99142456,1.88156128,1.97402954,1.88873291,1.5562439,1.93710327,1.91986084,1.75445557,2.24014282,2.00073242,1.91513062,1.88217163,1.9128418,1.80236816,1.81152344,1.81549072,2.42279053,2.09197998,2.10281372,2.02880859,1.93405151,1.91238403,2.10678101,2.03369141,0,14:12.6,0,0,1
46,1.96777344,1.95617676,2.30743408,2.1812439,2.23892212,2.15713501,2.13638306,2.0930481,1.74987793,1.64794922,2.03643799,1.89727783,2.1005249,2.06054688,2.05734253,1.96029663,1.96228027,1.95129395,0.72982788,1.83746338,2.02377319,1.88827515,1.97128296,1.88751221,1.50405884,1.9102478,1.89865112,1.7515564,2.27020264,2.01141357,1.93069458,1.87820435,1.88690186,1.80007935,1.82373047,1.81625366,2.35076904,2.10906982,2.08908081,2.03033447,1.93267822,1.90689087,2.10128784,2.03399658,0,14:12.8,0,0,1
47,1.96929932,1.95877075,2.28775024,2.18338013,2.19100952,2.16186523,2.16033936,2.09518433,1.75170898,1.64901733,2.00866699,1.89712524,2.13317871,2.05780029,2.06939697,1.95587158,1.93664551,1.95556641,0.70632935,1.85668945,2.01766968,1.89147949,1.90383911,1.88262939,1.52236938,1.93008423,1.92367554,1.75460815,2.26470947,2.0149231,1.92520142,1.87896729,1.86187744,1.80343628,1.81396484,1.82006836,2.41210938,2.11471558,2.10922241,2.03659058,1.93893433,1.90811157,2.12219238,2.03598022,0,14:12.8,0,0,1
48,1.99050903,1.96456909,2.27706909,2.19543457,2.17453003,2.15866089,2.17346191,2.10296631,1.75491333,1.6519165,1.98883057,1.89880371,2.10327148,2.05596924,2.06604004,1.95922852,1.9720459,1.9581604,0.70800781,1.84875488,1.98745728,1.88720703,1.85241699,1.88262939,1.64047241,1.95999146,1.92306519,1.7565918,2.19161987,2.00561523,1.90505981,1.87606812,1.89590454,1.8057251,1.81045532,1.82006836,2.48703003,2.1194458,2.10891724,2.03399658,1.93939209,1.90689087,2.11242676,2.04162598,0,14:12.9,0,0,1
49,1.97280884,1.97250366,2.30514526,2.1875,2.18338013,2.16217041,2.16903687,2.09976196,1.7539978,1.65435791,2.02072144,1.90002441,2.09899902,2.04727173,2.05581665,1.96090698,2.02911377,1.95846558,0.68664551,1.86218262,1.99523926,1.89331055,1.92123413,1.87957764,1.65481567,1.98486328,1.90444946,1.75323486,2.19009399,2.01766968,1.9342041,1.87744141,1.91238403,1.80587769,1.84326172,1.82693481,2.46429443,2.12585449,2.11929321,2.0350647,1.94122314,1.90887451,2.09945679,2.04971313,0,14:13.1,0,0,1
50,1.98562622,1.97647095,2.25646973,2.19741821,2.22045898,2.16873169,2.15682983,2.10876465,1.75262451,1.6569519,2.00668335,1.90261841,2.12188721,2.04498291,2.06100464,1.96395874,1.95648193,1.96670532,0.69229126,1.85089111,2.00088501,1.89285278,1.9354248,1.88446045,1.55502319,1.96472168,1.90673828,1.76055908,2.2605896,2.01889038,1.91513062,1.88034058,1.94366455,1.80252075,1.84631348,1.82983398,2.37731934,2.12509155,2.11257935,2.03948975,1.94213867,1.91162109,2.11624146,2.05383301,0,14:13.2,0,0,1
51,1.98532104,1.978302,2.27981567,2.19924927,2.24197388,2.17575073,2.18032837,2.11700439,1.74530029,1.65390015,2.02804565,1.91070557,2.18261719,2.06604004,2.06130981,1.97311401,1.95709229,1.97128296,0.67932129,1.84173584,2.03796387,1.90048218,1.94915771,1.89086914,1.46484375,1.97113037,1.92382813,1.76513672,2.27050781,2.02255249,1.91848755,1.88430786,1.91436768,1.81274414,1.84127808,1.83654785,2.46307373,2.11654663,2.09915161,2.04284668,1.93908691,1.91619873,2.12753296,2.05993652,0,14:13.2,0,0,1
52,1.97509766,1.97967529,2.36450195,2.19345093,2.20993042,2.17163086,2.16583252,2.12280273,1.73919678,1.64871216,2.03323364,1.91452026,2.16796875,2.07000732,2.07672119,1.978302,1.98638916,1.97525024,0.7093811,1.84326172,2.03903198,1.89804077,1.99325562,1.90338135,1.72515869,1.95877075,1.92169189,1.77062988,2.31582642,2.03323364,1.9090271,1.88720703,1.88232422,1.82006836,1.83380127,1.84494019,2.45056152,2.12860107,2.10067749,2.04116821,1.93969727,1.91940308,2.13150024,2.06924438,0,14:13.3,0,0,1
53,1.99325562,1.97662354,2.32818604,2.2052002,2.22793579,2.1786499,2.16842651,2.11837769,1.74057007,1.64794922,2.04315186,1.91970825,2.12310791,2.07366943,2.07748413,1.97845459,1.97799683,1.97662354,0.70541382,1.82373047,2.01553345,1.90078735,1.95404053,1.90170288,1.62277222,1.94091797,1.91558838,1.77001953,2.30239868,2.02728271,1.93084717,1.88735962,1.88156128,1.81793213,1.84570313,1.84280396,2.46688843,2.13165283,2.10281372,2.0413208,1.94015503,1.91955566,2.12234497,2.06802368,0,14:13.5,0,0,1
54,1.99813843,1.96731567,2.29202271,2.2076416,2.23937988,2.17666626,2.13287354,2.10449219,1.73568726,1.64352417,2.03887939,1.91711426,2.06069946,2.07778931,2.08129883,1.96777344,1.93588257,1.94976807,0.71517944,1.83792114,2.02056885,1.89315796,1.92520142,1.89743042,1.54418945,1.96350098,1.90139771,1.76101685,2.27981567,2.02011108,1.92138672,1.88781738,1.90750122,1.81045532,1.81747437,1.82617188,2.29568481,2.1105957,2.10891724,2.03613281,1.93557739,1.91497803,2.10845947,2.04650879,0,14:13.6,0,0,1
55,1.97982788,1.95541382,2.26745605,2.19268799,2.1913147,2.16308594,2.12265015,2.09365845,1.7350769,1.6394043,2.02392578,1.91116333,2.11090088,2.07733154,2.07366943,1.96212769,1.9631958,1.94335938,0.66879272,1.82617188,2.03765869,1.88583374,1.925354,1.88858032,1.53167725,1.97021484,1.90078735,1.75140381,2.27142334,2.01004028,1.92474365,1.88491821,1.8951416,1.80236816,1.8145752,1.81838989,2.31750488,2.08572388,2.10067749,2.02957153,1.93252563,1.91040039,2.10586548,2.03338623,0,14:13.6,0,0,1
56,1.9859314,1.95953369,2.30621338,2.18582153,2.26013184,2.16308594,2.12921143,2.08862305,1.7338562,1.6394043,2.02148438,1.90628052,2.16705322,2.06985474,2.07275391,1.96228027,1.90734863,1.94320679,0.67001343,1.84280396,2.00927734,1.88934326,1.93099976,1.88140869,1.48971558,1.95343018,1.90475464,1.7527771,2.2303772,2.00912476,1.92123413,1.88354492,1.87469482,1.80419922,1.83670044,1.81594849,2.43453979,2.09838867,2.10510254,2.03552246,1.93344116,1.90979004,2.10449219,2.03201294,0,14:13.8,0,0,1
57,1.97692871,1.95877075,2.25662231,2.19192505,2.18582153,2.16278076,2.12554932,2.09823608,1.7364502,1.63955688,2.00866699,1.90658569,2.16339111,2.05917358,2.07611084,1.96426392,1.92504883,1.94625854,0.67138672,1.83792114,2.00088501,1.88934326,1.97357178,1.88339233,1.52603149,1.91513062,1.90689087,1.75064087,2.23968506,2.01004028,1.92642212,1.88079834,1.90872192,1.80130005,1.8208313,1.8196106,2.35809326,2.11914063,2.10494995,2.03186035,1.93527222,1.90917969,2.10693359,2.03475952,0,14:13.9,0,0,1
58,1.96884155,1.96380615,2.2869873,2.18612671,2.19711304,2.16278076,2.17208862,2.09838867,1.7364502,1.64108276,2.0401001,1.902771,2.16567993,2.06344604,2.08374023,1.96090698,1.95404053,1.95205688,0.68054199,1.82876587,1.99401855,1.88980103,1.92932129,1.88278198,1.5574646,1.94778442,1.90444946,1.75033569,2.27706909,2.01431274,1.92123413,1.88522339,1.89453125,1.80648804,1.8347168,1.82128906,2.36129761,2.09823608,2.10357666,2.03796387,1.9342041,1.91268921,2.11395264,2.0401001,0,14:13.9,0,0,1
59,1.99127197,1.96868896,2.30560303,2.19406128,2.20077515,2.16506958,2.1282959,2.10098267,1.73904419,1.64550781,2.02056885,1.90383911,2.18032837,2.06710815,2.05780029,1.9581604,1.94717407,1.96212769,0.68649292,1.83181763,1.98196411,1.89056396,1.97280884,1.8888855,1.50177002,1.95922852,1.90307617,1.75338745,2.15057373,2.01873779,1.90261841,1.88278198,1.8901062,1.80679321,1.81869507,1.82510376,2.41439819,2.10342407,2.11669922,2.03872681,1.9342041,1.91101074,2.12295532,2.0463562,0,14:14.1,0,0,1
60,1.99523926,1.97143555,2.33352661,2.19512939,2.22671509,2.16888428,2.11273193,2.10479736,1.7414856,1.65023804,2.02011108,1.90368652,2.15698242,2.06314087,2.06939697,1.96411133,1.98226929,1.96411133,0.68206787,1.85348511,1.97357178,1.89147949,1.96670532,1.89239502,1.45980835,1.9569397,1.90994263,1.75567627,2.19894409,2.01599121,1.91940308,1.88644409,1.87759399,1.80664063,1.8296814,1.83166504,2.4055481,2.14035034,2.10906982,2.04696655,1.93634033,1.91757202,2.12265015,2.05444336,0,14:14.2,0,0,1
"""  # noqa: E501


CONTENTS[
    "1.25"
] = b"""\
Header
File Version,1.25
Patient Information
ID,REDACTED
Name,
Comment,sentence,,
Birth Date,1999/09/09
Age, 99y
Sex,Male
Analyze Information
AnalyzeMode,Continuous
Pre Time[s],9.0
Post Time[s],7.0
Recovery Time[s],12.0
Base Time[s],5
Fitting Degree,1
HPF[Hz],No Filter
LPF[Hz],No Filter
Moving Average[s],0.1
Measure Information
Date,2020/02/02 11:20
Probe Type,adult
Mode,3x11
Wave[nm],695,830
Wave Length,CH1(703.6),CH1(829.0),CH2(703.9),CH2(829.3),CH3(703.9),CH3(829.3),CH4(703.9),CH4(828.8),CH5(703.9),CH5(828.8),CH6(703.1),CH6(828.8),CH7(703.1),CH7(828.8),CH8(702.9),CH8(829.0),CH9(702.9),CH9(829.0),CH10(703.6),CH10(829.0),CH11(703.6),CH11(829.0),CH12(703.4),CH12(829.0),CH13(703.9),CH13(829.3),CH14(703.6),CH14(828.5),CH15(703.9),CH15(828.8),CH16(704.1),CH16(829.8),CH17(703.1),CH17(828.8),CH18(704.4),CH18(829.0),CH19(702.9),CH19(829.0),CH20(703.9),CH20(828.8),CH21(703.6),CH21(829.0),CH22(703.4),CH22(829.0),CH23(703.4),CH23(829.0),CH24(703.6),CH24(828.5),CH25(703.6),CH25(828.5),CH26(704.1),CH26(829.8),CH27(704.1),CH27(829.8),CH28(704.4),CH28(829.0),CH29(704.4),CH29(829.0),CH30(703.9),CH30(828.8),CH31(703.9),CH31(828.8),CH32(704.1),CH32(828.8),CH33(703.4),CH33(829.0),CH34(703.1),CH34(828.8),CH35(703.6),CH35(828.5),CH36(704.4),CH36(828.8),CH37(704.1),CH37(829.8),CH38(703.6),CH38(828.5),CH39(704.4),CH39(829.0),CH40(703.9),CH40(829.0),CH41(703.9),CH41(828.8),CH42(704.4),CH42(829.0),CH43(704.1),CH43(828.8),CH44(703.1),CH44(828.8),CH45(703.1),CH45(828.8),CH46(704.4),CH46(828.8),CH47(704.4),CH47(828.8),CH48(703.6),CH48(828.5),CH49(703.6),CH49(828.5),CH50(703.9),CH50(829.0),CH51(703.9),CH51(829.0),CH52(704.4),CH52(829.0)
Analog Gain,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,6.58823500,6.58823500,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,6.58823500,6.58823500,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,6.58823500,6.58823500,2.74509800,2.74509800,1.25490200,1.25490200,4.07843100,4.07843100,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,2.74509800,2.74509800,2.74509800,2.74509800,4.07843100,4.07843100,4.07843100,4.07843100,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200,1.25490200
Digital Gain,5.99000000,3.61000000,1.93000000,1.12000000,5.43000000,3.20000000,7.11000000,4.02000000,11.82000000,6.06000000,16.42000000,7.48000000,15.82000000,7.56000000,12.13000000,6.80000000,14.62000000,7.78000000,5.35000000,2.57000000,10.27000000,5.01000000,52.84000000,26.95000000,9.75000000,6.12000000,6.97000000,3.99000000,11.31000000,5.91000000,2.26000000,1.15000000,9.49000000,4.58000000,3.21000000,2.25000000,2.97000000,1.65000000,12.34000000,6.77000000,33.81000000,17.23000000,7.10000000,2.92000000,12.78000000,7.02000000,17.47000000,9.94000000,16.93000000,9.55000000,6.87000000,3.47000000,5.96000000,3.07000000,20.56000000,13.42000000,3.67000000,2.53000000,17.39000000,8.72000000,2.96000000,1.61000000,14.25000000,5.67000000,8.87000000,4.21000000,21.68000000,11.89000000,12.24000000,6.47000000,28.07000000,16.60000000,13.95000000,7.05000000,9.37000000,5.53000000,18.15000000,11.62000000,39.72000000,19.87000000,20.78000000,9.56000000,91.12000000,43.86000000,17.86000000,7.84000000,5.47000000,3.40000000,4.83000000,3.38000000,7.61000000,5.15000000,4.65000000,2.86000000,1.33000000,0.88000000,16.06000000,11.46000000,11.94000000,6.47000000,25.92000000,12.83000000,4.35000000,2.44000000
Sampling Period[s],0.1
StimType,STIM
Stim Time[s]
A,15,B,15,C,15,D,15,E,15,F,15,G,15,H,15,I,15,J,15
Repeat Count,20
Exception Ch,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0






Data
Probe1,CH1(703.6),CH1(829.0),CH2(703.9),CH2(829.3),CH3(703.9),CH3(829.3),CH4(703.9),CH4(828.8),CH5(703.9),CH5(828.8),CH6(703.1),CH6(828.8),CH7(703.1),CH7(828.8),CH8(702.9),CH8(829.0),CH9(702.9),CH9(829.0),CH10(703.6),CH10(829.0),CH11(703.6),CH11(829.0),CH12(703.4),CH12(829.0),CH13(703.9),CH13(829.3),CH14(703.6),CH14(828.5),CH15(703.9),CH15(828.8),CH16(704.1),CH16(829.8),CH17(703.1),CH17(828.8),CH18(704.4),CH18(829.0),CH19(702.9),CH19(829.0),CH20(703.9),CH20(828.8),CH21(703.6),CH21(829.0),CH22(703.4),CH22(829.0),CH23(703.4),CH23(829.0),CH24(703.6),CH24(828.5),CH25(703.6),CH25(828.5),CH26(704.1),CH26(829.8),CH27(704.1),CH27(829.8),CH28(704.4),CH28(829.0),CH29(704.4),CH29(829.0),CH30(703.9),CH30(828.8),CH31(703.9),CH31(828.8),CH32(704.1),CH32(828.8),CH33(703.4),CH33(829.0),CH34(703.1),CH34(828.8),CH35(703.6),CH35(828.5),CH36(704.4),CH36(828.8),CH37(704.1),CH37(829.8),CH38(703.6),CH38(828.5),CH39(704.4),CH39(829.0),CH40(703.9),CH40(829.0),CH41(703.9),CH41(828.8),CH42(704.4),CH42(829.0),CH43(704.1),CH43(828.8),CH44(703.1),CH44(828.8),CH45(703.1),CH45(828.8),CH46(704.4),CH46(828.8),CH47(704.4),CH47(828.8),CH48(703.6),CH48(828.5),CH49(703.6),CH49(828.5),CH50(703.9),CH50(829.0),CH51(703.9),CH51(829.0),CH52(704.4),CH52(829.0),Mark,Time,BodyMovement,RemovalMark,PreScan
1,2.01614380,2.01263428,2.01354980,2.01248169,2.02392578,2.01644897,2.02453613,2.01934814,2.01873779,2.01919556,2.02087402,2.01889038,2.02621460,2.03781128,2.02423096,2.02011108,2.00881958,2.00637817,2.02087402,2.01644897,2.02209473,2.00988770,2.03155518,2.01919556,2.02148438,2.01370239,2.01324463,2.00973511,2.00973511,2.01477051,2.02117920,2.02636719,2.00805664,2.01583862,2.02529907,2.03033447,2.00424194,1.99935913,2.01202393,2.01568604,2.01583862,2.01095581,2.02545166,2.01721191,2.02758789,2.02957153,2.00622559,2.00073242,2.00759888,2.00485229,2.02041626,2.03094482,2.01782227,2.01675415,2.00820923,2.01110840,2.01354980,2.00637817,2.01538086,2.00271606,2.01156616,2.01217651,2.01736450,1.99386597,2.03353882,2.02362061,2.03598022,2.02728271,2.00866699,1.99676514,2.00714111,2.00744629,2.02087402,2.01492310,2.01202393,2.01644897,2.00057983,1.99584961,2.01370239,1.99050903,1.99783325,1.99371338,2.01431274,1.99920654,1.99630737,1.97814941,2.02545166,2.01126099,2.02102661,2.00790405,2.00912476,1.99813843,2.01156616,1.99981689,1.99829102,1.99523926,2.01141357,2.00424194,2.00164795,1.98730469,2.03948975,2.00637817,2.00958252,1.99615479,0,15:40:49.15,0,0,1
2,2.02087402,2.02194214,2.01782227,2.02026367,2.02743530,2.02453613,2.02789307,2.02758789,2.02255249,2.02880859,2.02819824,2.02880859,2.03445435,2.04879761,2.02850342,2.02865601,2.01583862,2.01477051,2.02529907,2.02499390,2.03033447,2.02178955,2.03308105,2.02636719,2.02545166,2.02224731,2.01690674,2.01751709,2.01522827,2.02423096,2.02590942,2.03536987,2.01507568,2.02850342,2.03033447,2.04040527,2.00729370,2.00546265,2.01431274,2.02407837,2.02224731,2.02346802,2.02972412,2.02682495,2.03186035,2.03720093,2.01171875,2.00851440,2.00897217,2.01370239,2.02468872,2.04177856,2.02224731,2.02606201,2.01339722,2.02499390,2.01766968,2.01583862,2.01690674,2.00851440,2.01568604,2.02117920,2.02087402,2.00164795,2.03689575,2.03247070,2.04132080,2.03613281,2.01110840,2.00332642,2.01293945,2.01583862,2.02560425,2.02224731,2.01828003,2.02621460,2.00637817,2.00607300,2.01431274,1.99890137,2.00714111,1.99813843,2.01721191,2.00942993,1.99874878,1.98593140,2.02941895,2.02087402,2.02743530,2.01690674,2.01263428,2.00668335,2.01690674,2.00592041,2.00210571,2.00210571,2.01568604,2.01110840,2.00378418,1.99447632,2.04177856,2.01278687,2.01217651,2.00210571,0,15:40:49.26,0,0,1
3,2.01614380,2.01263428,2.01354980,2.01248169,2.02392578,2.01644897,2.02453613,2.01934814,2.01873779,2.01919556,2.02087402,2.01889038,2.02621460,2.03781128,2.02423096,2.02011108,2.00881958,2.00637817,2.02087402,2.01644897,2.02209473,2.00988770,2.03155518,2.01919556,2.02148438,2.01370239,2.01324463,2.00973511,2.00973511,2.01477051,2.02117920,2.02636719,2.00805664,2.01583862,2.02529907,2.03033447,2.00424194,1.99935913,2.01202393,2.01568604,2.01583862,2.01095581,2.02545166,2.01721191,2.02758789,2.02957153,2.00622559,2.00073242,2.00759888,2.00485229,2.02041626,2.03094482,2.01782227,2.01675415,2.00820923,2.01110840,2.01354980,2.00637817,2.01538086,2.00271606,2.01156616,2.01217651,2.01736450,1.99386597,2.03353882,2.02362061,2.03598022,2.02728271,2.00866699,1.99676514,2.00714111,2.00744629,2.02087402,2.01492310,2.01202393,2.01644897,2.00057983,1.99584961,2.01370239,1.99050903,1.99783325,1.99371338,2.01431274,1.99920654,1.99630737,1.97814941,2.02545166,2.01126099,2.02102661,2.00790405,2.00912476,1.99813843,2.01156616,1.99981689,1.99829102,1.99523926,2.01141357,2.00424194,2.00164795,1.98730469,2.03948975,2.00637817,2.00958252,1.99615479,0,15:40:49.15,0,0,1
4,2.02087402,2.02194214,2.01782227,2.02026367,2.02743530,2.02453613,2.02789307,2.02758789,2.02255249,2.02880859,2.02819824,2.02880859,2.03445435,2.04879761,2.02850342,2.02865601,2.01583862,2.01477051,2.02529907,2.02499390,2.03033447,2.02178955,2.03308105,2.02636719,2.02545166,2.02224731,2.01690674,2.01751709,2.01522827,2.02423096,2.02590942,2.03536987,2.01507568,2.02850342,2.03033447,2.04040527,2.00729370,2.00546265,2.01431274,2.02407837,2.02224731,2.02346802,2.02972412,2.02682495,2.03186035,2.03720093,2.01171875,2.00851440,2.00897217,2.01370239,2.02468872,2.04177856,2.02224731,2.02606201,2.01339722,2.02499390,2.01766968,2.01583862,2.01690674,2.00851440,2.01568604,2.02117920,2.02087402,2.00164795,2.03689575,2.03247070,2.04132080,2.03613281,2.01110840,2.00332642,2.01293945,2.01583862,2.02560425,2.02224731,2.01828003,2.02621460,2.00637817,2.00607300,2.01431274,1.99890137,2.00714111,1.99813843,2.01721191,2.00942993,1.99874878,1.98593140,2.02941895,2.02087402,2.02743530,2.01690674,2.01263428,2.00668335,2.01690674,2.00592041,2.00210571,2.00210571,2.01568604,2.01110840,2.00378418,1.99447632,2.04177856,2.01278687,2.01217651,2.00210571,0,15:40:49.26,0,0,1
5,2.01614380,2.01263428,2.01354980,2.01248169,2.02392578,2.01644897,2.02453613,2.01934814,2.01873779,2.01919556,2.02087402,2.01889038,2.02621460,2.03781128,2.02423096,2.02011108,2.00881958,2.00637817,2.02087402,2.01644897,2.02209473,2.00988770,2.03155518,2.01919556,2.02148438,2.01370239,2.01324463,2.00973511,2.00973511,2.01477051,2.02117920,2.02636719,2.00805664,2.01583862,2.02529907,2.03033447,2.00424194,1.99935913,2.01202393,2.01568604,2.01583862,2.01095581,2.02545166,2.01721191,2.02758789,2.02957153,2.00622559,2.00073242,2.00759888,2.00485229,2.02041626,2.03094482,2.01782227,2.01675415,2.00820923,2.01110840,2.01354980,2.00637817,2.01538086,2.00271606,2.01156616,2.01217651,2.01736450,1.99386597,2.03353882,2.02362061,2.03598022,2.02728271,2.00866699,1.99676514,2.00714111,2.00744629,2.02087402,2.01492310,2.01202393,2.01644897,2.00057983,1.99584961,2.01370239,1.99050903,1.99783325,1.99371338,2.01431274,1.99920654,1.99630737,1.97814941,2.02545166,2.01126099,2.02102661,2.00790405,2.00912476,1.99813843,2.01156616,1.99981689,1.99829102,1.99523926,2.01141357,2.00424194,2.00164795,1.98730469,2.03948975,2.00637817,2.00958252,1.99615479,0,15:40:49.15,0,0,1
6,2.02087402,2.02194214,2.01782227,2.02026367,2.02743530,2.02453613,2.02789307,2.02758789,2.02255249,2.02880859,2.02819824,2.02880859,2.03445435,2.04879761,2.02850342,2.02865601,2.01583862,2.01477051,2.02529907,2.02499390,2.03033447,2.02178955,2.03308105,2.02636719,2.02545166,2.02224731,2.01690674,2.01751709,2.01522827,2.02423096,2.02590942,2.03536987,2.01507568,2.02850342,2.03033447,2.04040527,2.00729370,2.00546265,2.01431274,2.02407837,2.02224731,2.02346802,2.02972412,2.02682495,2.03186035,2.03720093,2.01171875,2.00851440,2.00897217,2.01370239,2.02468872,2.04177856,2.02224731,2.02606201,2.01339722,2.02499390,2.01766968,2.01583862,2.01690674,2.00851440,2.01568604,2.02117920,2.02087402,2.00164795,2.03689575,2.03247070,2.04132080,2.03613281,2.01110840,2.00332642,2.01293945,2.01583862,2.02560425,2.02224731,2.01828003,2.02621460,2.00637817,2.00607300,2.01431274,1.99890137,2.00714111,1.99813843,2.01721191,2.00942993,1.99874878,1.98593140,2.02941895,2.02087402,2.02743530,2.01690674,2.01263428,2.00668335,2.01690674,2.00592041,2.00210571,2.00210571,2.01568604,2.01110840,2.00378418,1.99447632,2.04177856,2.01278687,2.01217651,2.00210571,0,15:40:49.26,0,0,1
7,2.01614380,2.01263428,2.01354980,2.01248169,2.02392578,2.01644897,2.02453613,2.01934814,2.01873779,2.01919556,2.02087402,2.01889038,2.02621460,2.03781128,2.02423096,2.02011108,2.00881958,2.00637817,2.02087402,2.01644897,2.02209473,2.00988770,2.03155518,2.01919556,2.02148438,2.01370239,2.01324463,2.00973511,2.00973511,2.01477051,2.02117920,2.02636719,2.00805664,2.01583862,2.02529907,2.03033447,2.00424194,1.99935913,2.01202393,2.01568604,2.01583862,2.01095581,2.02545166,2.01721191,2.02758789,2.02957153,2.00622559,2.00073242,2.00759888,2.00485229,2.02041626,2.03094482,2.01782227,2.01675415,2.00820923,2.01110840,2.01354980,2.00637817,2.01538086,2.00271606,2.01156616,2.01217651,2.01736450,1.99386597,2.03353882,2.02362061,2.03598022,2.02728271,2.00866699,1.99676514,2.00714111,2.00744629,2.02087402,2.01492310,2.01202393,2.01644897,2.00057983,1.99584961,2.01370239,1.99050903,1.99783325,1.99371338,2.01431274,1.99920654,1.99630737,1.97814941,2.02545166,2.01126099,2.02102661,2.00790405,2.00912476,1.99813843,2.01156616,1.99981689,1.99829102,1.99523926,2.01141357,2.00424194,2.00164795,1.98730469,2.03948975,2.00637817,2.00958252,1.99615479,0,15:40:49.15,0,0,1
8,2.02087402,2.02194214,2.01782227,2.02026367,2.02743530,2.02453613,2.02789307,2.02758789,2.02255249,2.02880859,2.02819824,2.02880859,2.03445435,2.04879761,2.02850342,2.02865601,2.01583862,2.01477051,2.02529907,2.02499390,2.03033447,2.02178955,2.03308105,2.02636719,2.02545166,2.02224731,2.01690674,2.01751709,2.01522827,2.02423096,2.02590942,2.03536987,2.01507568,2.02850342,2.03033447,2.04040527,2.00729370,2.00546265,2.01431274,2.02407837,2.02224731,2.02346802,2.02972412,2.02682495,2.03186035,2.03720093,2.01171875,2.00851440,2.00897217,2.01370239,2.02468872,2.04177856,2.02224731,2.02606201,2.01339722,2.02499390,2.01766968,2.01583862,2.01690674,2.00851440,2.01568604,2.02117920,2.02087402,2.00164795,2.03689575,2.03247070,2.04132080,2.03613281,2.01110840,2.00332642,2.01293945,2.01583862,2.02560425,2.02224731,2.01828003,2.02621460,2.00637817,2.00607300,2.01431274,1.99890137,2.00714111,1.99813843,2.01721191,2.00942993,1.99874878,1.98593140,2.02941895,2.02087402,2.02743530,2.01690674,2.01263428,2.00668335,2.01690674,2.00592041,2.00210571,2.00210571,2.01568604,2.01110840,2.00378418,1.99447632,2.04177856,2.01278687,2.01217651,2.00210571,0,15:40:49.26,0,0,1
9,2.01614380,2.01263428,2.01354980,2.01248169,2.02392578,2.01644897,2.02453613,2.01934814,2.01873779,2.01919556,2.02087402,2.01889038,2.02621460,2.03781128,2.02423096,2.02011108,2.00881958,2.00637817,2.02087402,2.01644897,2.02209473,2.00988770,2.03155518,2.01919556,2.02148438,2.01370239,2.01324463,2.00973511,2.00973511,2.01477051,2.02117920,2.02636719,2.00805664,2.01583862,2.02529907,2.03033447,2.00424194,1.99935913,2.01202393,2.01568604,2.01583862,2.01095581,2.02545166,2.01721191,2.02758789,2.02957153,2.00622559,2.00073242,2.00759888,2.00485229,2.02041626,2.03094482,2.01782227,2.01675415,2.00820923,2.01110840,2.01354980,2.00637817,2.01538086,2.00271606,2.01156616,2.01217651,2.01736450,1.99386597,2.03353882,2.02362061,2.03598022,2.02728271,2.00866699,1.99676514,2.00714111,2.00744629,2.02087402,2.01492310,2.01202393,2.01644897,2.00057983,1.99584961,2.01370239,1.99050903,1.99783325,1.99371338,2.01431274,1.99920654,1.99630737,1.97814941,2.02545166,2.01126099,2.02102661,2.00790405,2.00912476,1.99813843,2.01156616,1.99981689,1.99829102,1.99523926,2.01141357,2.00424194,2.00164795,1.98730469,2.03948975,2.00637817,2.00958252,1.99615479,0,15:40:49.15,0,0,1
10,2.02087402,2.02194214,2.01782227,2.02026367,2.02743530,2.02453613,2.02789307,2.02758789,2.02255249,2.02880859,2.02819824,2.02880859,2.03445435,2.04879761,2.02850342,2.02865601,2.01583862,2.01477051,2.02529907,2.02499390,2.03033447,2.02178955,2.03308105,2.02636719,2.02545166,2.02224731,2.01690674,2.01751709,2.01522827,2.02423096,2.02590942,2.03536987,2.01507568,2.02850342,2.03033447,2.04040527,2.00729370,2.00546265,2.01431274,2.02407837,2.02224731,2.02346802,2.02972412,2.02682495,2.03186035,2.03720093,2.01171875,2.00851440,2.00897217,2.01370239,2.02468872,2.04177856,2.02224731,2.02606201,2.01339722,2.02499390,2.01766968,2.01583862,2.01690674,2.00851440,2.01568604,2.02117920,2.02087402,2.00164795,2.03689575,2.03247070,2.04132080,2.03613281,2.01110840,2.00332642,2.01293945,2.01583862,2.02560425,2.02224731,2.01828003,2.02621460,2.00637817,2.00607300,2.01431274,1.99890137,2.00714111,1.99813843,2.01721191,2.00942993,1.99874878,1.98593140,2.02941895,2.02087402,2.02743530,2.01690674,2.01263428,2.00668335,2.01690674,2.00592041,2.00210571,2.00210571,2.01568604,2.01110840,2.00378418,1.99447632,2.04177856,2.01278687,2.01217651,2.00210571,0,15:40:49.26,0,0,1
"""  # noqa: E501


@pytest.mark.parametrize("preload", (True, False))
@pytest.mark.parametrize(
    "version, n_ch, n_times, lowpass, sex, date, end",
    [
        ("1.18", 48, 60, 0.1, 2, (2004, 5, 17, 5, 14, 0, 0), None),
        ("1.25", 108, 10, 5.0, 1, (2020, 2, 2, 11, 20, 0, 0), b"\r"),
        ("1.25", 108, 10, 5.0, 1, (2020, 2, 2, 11, 20, 0, 0), b"\n"),
        ("1.25", 108, 10, 5.0, 1, (2020, 2, 2, 11, 20, 0, 0), b"\r\n"),
        # Fake a dual-probe file
        (["1.18", "1.18"], 92, 60, 0.1, 2, (2004, 5, 17, 5, 14, 0, 0), None),
    ],
)
def test_hitachi_basic(
    preload, version, n_ch, n_times, lowpass, sex, date, end, tmp_path
):
    """Test NIRSport1 file with no saturation."""
    if not isinstance(version, list):
        versions = [version]
    else:
        versions = version
    del version
    fnames = list()
    for vi, v in enumerate(versions, 1):
        fname = tmp_path / f"test{vi}.csv"
        contents = CONTENTS[v].replace(f"Probe{vi - 1}".encode(), f"Probe{vi}".encode())
        if end is not None:
            contents = contents.replace(b"\r", b"\n").replace(b"\n\n", b"\n")
            contents = contents.replace(b"\n", end)
        with open(fname, "wb") as fid:
            fid.write(CONTENTS[v])
        fnames.append(fname)
        del fname
    raw = read_raw_hitachi(fnames, preload=preload, verbose=True)
    data = raw.get_data()
    assert data.shape == (n_ch, n_times)
    assert raw.info["sfreq"] == 10
    assert raw.info["lowpass"] == lowpass
    assert raw.info["subject_info"]["sex"] == sex
    assert np.isfinite(raw.get_data()).all()
    assert raw.info["meas_date"] == dt.datetime(*date, tzinfo=dt.timezone.utc)
    # bad distances (zero)
    distances = source_detector_distances(raw.info)
    want = [np.nan] * (n_ch - 4)
    assert_allclose(distances, want, atol=0.0)
    raw_od_bad = optical_density(raw)
    with pytest.warns(RuntimeWarning, match="will be zero"):
        beer_lambert_law(raw_od_bad, ppf=6)
    # bad distances (too big)
    if versions[0] == "1.18" and len(fnames) == 1:
        need = sum(([f"S{ii}", f"D{ii}"] for ii in range(1, 9)), [])[:-1]
        have = "P7 FC3 C3 CP3 P3 F5 FC5 C5 CP5 P5 F7 FT7 T7 TP7 F3".split()
        assert len(need) == len(have)
        mon = make_standard_montage("standard_1020")
        mon.rename_channels(dict(zip(have, need)))
        raw.set_montage(mon)
        raw_od_bad = optical_density(raw)
        with pytest.warns(RuntimeWarning, match="greater than 10 cm"):
            beer_lambert_law(raw_od_bad, ppf=6)
    # good distances
    mon = make_standard_montage("standard_1020")
    if versions[0] == "1.18":
        assert len(fnames) in (1, 2)
        need = sum(([f"S{ii}", f"D{ii}"] for ii in range(1, 9)), [])[:-1]
        have = "F3 FC3 C3 CP3 P3 F5 FC5 C5 CP5 P5 F7 FT7 T7 TP7 P7".split()
        assert len(need) == 15
        if len(fnames) == 2:
            need.extend(
                sum(
                    (
                        [f"S{ii}", f"D{jj}"]
                        for ii, jj in zip(range(9, 17), range(8, 16))
                    ),
                    [],
                )[:-1]
            )
            have.extend("F4 FC4 C4 CP4 P4 F6 FC6 C6 CP6 P6 F8 FT8 T8 TP8 P8".split())
            assert len(need) == 30
    else:
        assert len(fnames) == 1
        need = sum(([f"S{ii}", f"D{ii}"] for ii in range(1, 18)), [])[:-1]
        have = (
            "FT9 FT7 FC5 FC3 FC1 FCz FC2 FC4 FC6 FT8 FT10 "
            "T9 T7 C5 C3 C1 Cz C2 C4 C6 T8 T10 "
            "TP9 TP7 CP5 CP3 CP1 CPz CP2 CP4 CP6 TP8 TP10"
        ).split()
        assert len(need) == 33
    assert len(need) == len(have)
    for h in have:
        assert h in mon.ch_names
    mon.rename_channels(dict(zip(have, need)))
    for n in need:
        assert n in mon.ch_names
    raw.set_montage(mon)
    distances = source_detector_distances(raw.info)
    want = [0.03] * (n_ch - 4)
    assert_allclose(distances, want, atol=0.01)
    test_rank = "less" if n_times < n_ch else True
    _test_raw_reader(
        read_raw_hitachi, fname=fnames, boundary_decimal=1, test_rank=test_rank
    )  # low fs

    # TODO: eventually we should refactor these to be in
    # mne/io/tests/test_raw.py and run them for all fNIRS readers

    # OD
    raw_od = optical_density(raw)
    assert np.isfinite(raw_od.get_data()).all()
    sci = scalp_coupling_index(raw_od, verbose="error")
    lo, mi, hi = np.percentile(sci, [5, 50, 95])
    if versions[0] == "1.18":
        assert -0.1 < lo < 0.1  # not great
        assert 0.4 < mi < 0.5
        assert 0.8 < hi < 0.9
    else:
        assert 0.99 <= lo <= hi <= 1
    # TDDR
    raw_tddr = tddr(raw_od)
    data = raw_tddr.get_data("fnirs")
    assert np.isfinite(data.all())
    peaks = np.ptp(data, axis=-1)
    assert_array_less(1e-4, peaks, err_msg="TDDR too small")
    assert_array_less(peaks, 1, err_msg="TDDR too big")
    # HbO/HbR
    raw_tddr.set_montage(mon)
    raw_h = beer_lambert_law(raw_tddr, ppf=6)
    data = raw_h.get_data("fnirs")
    assert np.isfinite(data).all()
    assert data.shape == (n_ch - 4, n_times)
    peaks = np.ptp(data, axis=-1)
    assert_array_less(1e-10, peaks, err_msg="Beer-Lambert too small")
    assert_array_less(peaks, 1e-5, err_msg="Beer-Lambert too big")


# From Hitachi 2 Homer
KNOWN_PAIRS = {
    (3, 3, 2): (
        (0, 0),
        (1, 0),
        (0, 1),
        (2, 0),
        (1, 2),
        (2, 1),
        (2, 2),
        (3, 1),
        (2, 3),
        (4, 2),
        (3, 3),
        (4, 3),
        (5, 4),
        (6, 4),
        (5, 5),
        (7, 4),
        (6, 6),
        (7, 5),
        (7, 6),
        (8, 5),
        (7, 7),
        (9, 6),
        (8, 7),
        (9, 7),
    ),
    (3, 5, 1): (
        (0, 0),
        (1, 0),
        (1, 1),
        (2, 1),
        (0, 2),
        (3, 0),
        (1, 3),
        (4, 1),
        (2, 4),
        (3, 2),
        (3, 3),
        (4, 3),
        (4, 4),
        (5, 2),
        (3, 5),
        (6, 3),
        (4, 6),
        (7, 4),
        (5, 5),
        (6, 5),
        (6, 6),
        (7, 6),
    ),
    (4, 4, 1): (
        (0, 0),
        (1, 0),
        (1, 1),
        (0, 2),
        (2, 0),
        (1, 3),
        (3, 1),
        (2, 2),
        (2, 3),
        (3, 3),
        (4, 2),
        (2, 4),
        (5, 3),
        (3, 5),
        (4, 4),
        (5, 4),
        (5, 5),
        (4, 6),
        (6, 4),
        (5, 7),
        (7, 5),
        (6, 6),
        (6, 7),
        (7, 7),
    ),
    (3, 11, 1): (
        (0, 0),
        (1, 0),
        (1, 1),
        (2, 1),
        (2, 2),
        (3, 2),
        (3, 3),
        (4, 3),
        (4, 4),
        (5, 4),
        (0, 5),
        (6, 0),
        (1, 6),
        (7, 1),
        (2, 7),
        (8, 2),
        (3, 8),
        (9, 3),
        (4, 9),
        (10, 4),
        (5, 10),
        (6, 5),
        (6, 6),
        (7, 6),
        (7, 7),
        (8, 7),
        (8, 8),
        (9, 8),
        (9, 9),
        (10, 9),
        (10, 10),
        (11, 5),
        (6, 11),
        (12, 6),
        (7, 12),
        (13, 7),
        (8, 13),
        (14, 8),
        (9, 14),
        (15, 9),
        (10, 15),
        (16, 10),
        (11, 11),
        (12, 11),
        (12, 12),
        (13, 12),
        (13, 13),
        (14, 13),
        (14, 14),
        (15, 14),
        (15, 15),
        (16, 15),
    ),
}


@pytest.mark.parametrize("n_rows, n_cols, n", list(KNOWN_PAIRS))
def test_compute_pairs(n_rows, n_cols, n):
    """Test computation of S-D pairings."""
    want = KNOWN_PAIRS[(n_rows, n_cols, n)]
    got = _compute_pairs(n_rows, n_cols, n)
    assert got == want
