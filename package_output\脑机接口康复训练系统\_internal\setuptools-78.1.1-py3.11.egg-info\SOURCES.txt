LICENSE
MANIFEST.in
NEWS.rst
README.rst
conftest.py
exercises.py
launcher.c
mypy.ini
pyproject.toml
pytest.ini
setup.cfg
setup.py
tox.ini
_distutils_hack/__init__.py
_distutils_hack/override.py
docs/artwork.rst
docs/build_meta.rst
docs/conf.py
docs/history.rst
docs/index.rst
docs/pkg_resources.rst
docs/python 2 sunset.rst
docs/roadmap.rst
docs/setuptools.rst
docs/deprecated/changed_keywords.rst
docs/deprecated/commands.rst
docs/deprecated/dependency_links.rst
docs/deprecated/distutils-legacy.rst
docs/deprecated/easy_install.rst
docs/deprecated/functionalities.rst
docs/deprecated/index.rst
docs/deprecated/python_eggs.rst
docs/deprecated/resource_extraction.rst
docs/deprecated/zip_safe.rst
docs/deprecated/distutils/_setuptools_disclaimer.rst
docs/deprecated/distutils/apiref.rst
docs/deprecated/distutils/builtdist.rst
docs/deprecated/distutils/commandref.rst
docs/deprecated/distutils/configfile.rst
docs/deprecated/distutils/examples.rst
docs/deprecated/distutils/extending.rst
docs/deprecated/distutils/index.rst
docs/deprecated/distutils/introduction.rst
docs/deprecated/distutils/packageindex.rst
docs/deprecated/distutils/setupscript.rst
docs/deprecated/distutils/sourcedist.rst
docs/deprecated/distutils/uploading.rst
docs/development/developer-guide.rst
docs/development/index.rst
docs/development/releases.rst
docs/references/keywords.rst
docs/userguide/datafiles.rst
docs/userguide/declarative_config.rst
docs/userguide/dependency_management.rst
docs/userguide/development_mode.rst
docs/userguide/distribution.rst
docs/userguide/entry_point.rst
docs/userguide/ext_modules.rst
docs/userguide/extension.rst
docs/userguide/index.rst
docs/userguide/miscellaneous.rst
docs/userguide/package_discovery.rst
docs/userguide/pyproject_config.rst
docs/userguide/quickstart.rst
newsfragments/.gitignore
newsfragments/README.rst
pkg_resources/__init__.py
pkg_resources/api_tests.txt
pkg_resources/py.typed
pkg_resources/tests/__init__.py
pkg_resources/tests/test_find_distributions.py
pkg_resources/tests/test_integration_zope_interface.py
pkg_resources/tests/test_markers.py
pkg_resources/tests/test_pkg_resources.py
pkg_resources/tests/test_resources.py
pkg_resources/tests/test_working_set.py
pkg_resources/tests/data/my-test-package-source/setup.cfg
pkg_resources/tests/data/my-test-package-source/setup.py
pkg_resources/tests/data/my-test-package-zip/my-test-package.zip
pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO
pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt
pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt
pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt
pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe
pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg
setuptools/__init__.py
setuptools/_core_metadata.py
setuptools/_entry_points.py
setuptools/_imp.py
setuptools/_importlib.py
setuptools/_itertools.py
setuptools/_normalization.py
setuptools/_path.py
setuptools/_reqs.py
setuptools/_shutil.py
setuptools/_static.py
setuptools/archive_util.py
setuptools/build_meta.py
setuptools/cli-32.exe
setuptools/cli-64.exe
setuptools/cli-arm64.exe
setuptools/cli.exe
setuptools/depends.py
setuptools/discovery.py
setuptools/dist.py
setuptools/errors.py
setuptools/extension.py
setuptools/glob.py
setuptools/gui-32.exe
setuptools/gui-64.exe
setuptools/gui-arm64.exe
setuptools/gui.exe
setuptools/installer.py
setuptools/launch.py
setuptools/logging.py
setuptools/modified.py
setuptools/monkey.py
setuptools/msvc.py
setuptools/namespaces.py
setuptools/package_index.py
setuptools/sandbox.py
setuptools/script (dev).tmpl
setuptools/script.tmpl
setuptools/unicode_utils.py
setuptools/version.py
setuptools/warnings.py
setuptools/wheel.py
setuptools/windows_support.py
setuptools.egg-info/PKG-INFO
setuptools.egg-info/SOURCES.txt
setuptools.egg-info/dependency_links.txt
setuptools.egg-info/entry_points.txt
setuptools.egg-info/requires.txt
setuptools.egg-info/top_level.txt
setuptools/_distutils/__init__.py
setuptools/_distutils/_log.py
setuptools/_distutils/_macos_compat.py
setuptools/_distutils/_modified.py
setuptools/_distutils/_msvccompiler.py
setuptools/_distutils/archive_util.py
setuptools/_distutils/ccompiler.py
setuptools/_distutils/cmd.py
setuptools/_distutils/core.py
setuptools/_distutils/cygwinccompiler.py
setuptools/_distutils/debug.py
setuptools/_distutils/dep_util.py
setuptools/_distutils/dir_util.py
setuptools/_distutils/dist.py
setuptools/_distutils/errors.py
setuptools/_distutils/extension.py
setuptools/_distutils/fancy_getopt.py
setuptools/_distutils/file_util.py
setuptools/_distutils/filelist.py
setuptools/_distutils/log.py
setuptools/_distutils/spawn.py
setuptools/_distutils/sysconfig.py
setuptools/_distutils/text_file.py
setuptools/_distutils/unixccompiler.py
setuptools/_distutils/util.py
setuptools/_distutils/version.py
setuptools/_distutils/versionpredicate.py
setuptools/_distutils/zosccompiler.py
setuptools/_distutils/command/__init__.py
setuptools/_distutils/command/_framework_compat.py
setuptools/_distutils/command/bdist.py
setuptools/_distutils/command/bdist_dumb.py
setuptools/_distutils/command/bdist_rpm.py
setuptools/_distutils/command/build.py
setuptools/_distutils/command/build_clib.py
setuptools/_distutils/command/build_ext.py
setuptools/_distutils/command/build_py.py
setuptools/_distutils/command/build_scripts.py
setuptools/_distutils/command/check.py
setuptools/_distutils/command/clean.py
setuptools/_distutils/command/config.py
setuptools/_distutils/command/install.py
setuptools/_distutils/command/install_data.py
setuptools/_distutils/command/install_egg_info.py
setuptools/_distutils/command/install_headers.py
setuptools/_distutils/command/install_lib.py
setuptools/_distutils/command/install_scripts.py
setuptools/_distutils/command/sdist.py
setuptools/_distutils/compat/__init__.py
setuptools/_distutils/compat/numpy.py
setuptools/_distutils/compat/py39.py
setuptools/_distutils/compilers/C/base.py
setuptools/_distutils/compilers/C/cygwin.py
setuptools/_distutils/compilers/C/errors.py
setuptools/_distutils/compilers/C/msvc.py
setuptools/_distutils/compilers/C/unix.py
setuptools/_distutils/compilers/C/zos.py
setuptools/_distutils/compilers/C/tests/test_base.py
setuptools/_distutils/compilers/C/tests/test_cygwin.py
setuptools/_distutils/compilers/C/tests/test_mingw.py
setuptools/_distutils/compilers/C/tests/test_msvc.py
setuptools/_distutils/compilers/C/tests/test_unix.py
setuptools/_distutils/tests/__init__.py
setuptools/_distutils/tests/support.py
setuptools/_distutils/tests/test_archive_util.py
setuptools/_distutils/tests/test_bdist.py
setuptools/_distutils/tests/test_bdist_dumb.py
setuptools/_distutils/tests/test_bdist_rpm.py
setuptools/_distutils/tests/test_build.py
setuptools/_distutils/tests/test_build_clib.py
setuptools/_distutils/tests/test_build_ext.py
setuptools/_distutils/tests/test_build_py.py
setuptools/_distutils/tests/test_build_scripts.py
setuptools/_distutils/tests/test_check.py
setuptools/_distutils/tests/test_clean.py
setuptools/_distutils/tests/test_cmd.py
setuptools/_distutils/tests/test_config_cmd.py
setuptools/_distutils/tests/test_core.py
setuptools/_distutils/tests/test_dir_util.py
setuptools/_distutils/tests/test_dist.py
setuptools/_distutils/tests/test_extension.py
setuptools/_distutils/tests/test_file_util.py
setuptools/_distutils/tests/test_filelist.py
setuptools/_distutils/tests/test_install.py
setuptools/_distutils/tests/test_install_data.py
setuptools/_distutils/tests/test_install_headers.py
setuptools/_distutils/tests/test_install_lib.py
setuptools/_distutils/tests/test_install_scripts.py
setuptools/_distutils/tests/test_log.py
setuptools/_distutils/tests/test_modified.py
setuptools/_distutils/tests/test_sdist.py
setuptools/_distutils/tests/test_spawn.py
setuptools/_distutils/tests/test_sysconfig.py
setuptools/_distutils/tests/test_text_file.py
setuptools/_distutils/tests/test_util.py
setuptools/_distutils/tests/test_version.py
setuptools/_distutils/tests/test_versionpredicate.py
setuptools/_distutils/tests/unix_compat.py
setuptools/_distutils/tests/compat/__init__.py
setuptools/_distutils/tests/compat/py39.py
setuptools/_vendor/ruff.toml
setuptools/_vendor/typing_extensions.py
setuptools/_vendor/autocommand/__init__.py
setuptools/_vendor/autocommand/autoasync.py
setuptools/_vendor/autocommand/autocommand.py
setuptools/_vendor/autocommand/automain.py
setuptools/_vendor/autocommand/autoparse.py
setuptools/_vendor/autocommand/errors.py
setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER
setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE
setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA
setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD
setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL
setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt
setuptools/_vendor/backports/__init__.py
setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER
setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE
setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA
setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD
setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED
setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL
setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt
setuptools/_vendor/backports/tarfile/__init__.py
setuptools/_vendor/backports/tarfile/__main__.py
setuptools/_vendor/backports/tarfile/compat/__init__.py
setuptools/_vendor/backports/tarfile/compat/py38.py
setuptools/_vendor/importlib_metadata/__init__.py
setuptools/_vendor/importlib_metadata/_adapters.py
setuptools/_vendor/importlib_metadata/_collections.py
setuptools/_vendor/importlib_metadata/_compat.py
setuptools/_vendor/importlib_metadata/_functools.py
setuptools/_vendor/importlib_metadata/_itertools.py
setuptools/_vendor/importlib_metadata/_meta.py
setuptools/_vendor/importlib_metadata/_text.py
setuptools/_vendor/importlib_metadata/diagnose.py
setuptools/_vendor/importlib_metadata/py.typed
setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER
setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE
setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA
setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD
setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED
setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL
setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt
setuptools/_vendor/importlib_metadata/compat/__init__.py
setuptools/_vendor/importlib_metadata/compat/py311.py
setuptools/_vendor/importlib_metadata/compat/py39.py
setuptools/_vendor/inflect/__init__.py
setuptools/_vendor/inflect/py.typed
setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER
setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE
setuptools/_vendor/inflect-7.3.1.dist-info/METADATA
setuptools/_vendor/inflect-7.3.1.dist-info/RECORD
setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL
setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt
setuptools/_vendor/inflect/compat/__init__.py
setuptools/_vendor/inflect/compat/py38.py
setuptools/_vendor/jaraco/context.py
setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER
setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE
setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA
setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD
setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED
setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL
setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt
setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER
setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE
setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA
setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD
setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL
setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt
setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER
setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE
setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA
setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD
setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL
setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt
setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER
setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE
setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA
setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD
setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED
setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL
setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt
setuptools/_vendor/jaraco/collections/__init__.py
setuptools/_vendor/jaraco/collections/py.typed
setuptools/_vendor/jaraco/functools/__init__.py
setuptools/_vendor/jaraco/functools/__init__.pyi
setuptools/_vendor/jaraco/functools/py.typed
setuptools/_vendor/jaraco/text/Lorem ipsum.txt
setuptools/_vendor/jaraco/text/__init__.py
setuptools/_vendor/jaraco/text/layouts.py
setuptools/_vendor/jaraco/text/show-newlines.py
setuptools/_vendor/jaraco/text/strip-prefix.py
setuptools/_vendor/jaraco/text/to-dvorak.py
setuptools/_vendor/jaraco/text/to-qwerty.py
setuptools/_vendor/more_itertools/__init__.py
setuptools/_vendor/more_itertools/__init__.pyi
setuptools/_vendor/more_itertools/more.py
setuptools/_vendor/more_itertools/more.pyi
setuptools/_vendor/more_itertools/py.typed
setuptools/_vendor/more_itertools/recipes.py
setuptools/_vendor/more_itertools/recipes.pyi
setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER
setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE
setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA
setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD
setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED
setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL
setuptools/_vendor/packaging/__init__.py
setuptools/_vendor/packaging/_elffile.py
setuptools/_vendor/packaging/_manylinux.py
setuptools/_vendor/packaging/_musllinux.py
setuptools/_vendor/packaging/_parser.py
setuptools/_vendor/packaging/_structures.py
setuptools/_vendor/packaging/_tokenizer.py
setuptools/_vendor/packaging/markers.py
setuptools/_vendor/packaging/metadata.py
setuptools/_vendor/packaging/py.typed
setuptools/_vendor/packaging/requirements.py
setuptools/_vendor/packaging/specifiers.py
setuptools/_vendor/packaging/tags.py
setuptools/_vendor/packaging/utils.py
setuptools/_vendor/packaging/version.py
setuptools/_vendor/packaging-24.2.dist-info/INSTALLER
setuptools/_vendor/packaging-24.2.dist-info/LICENSE
setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE
setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD
setuptools/_vendor/packaging-24.2.dist-info/METADATA
setuptools/_vendor/packaging-24.2.dist-info/RECORD
setuptools/_vendor/packaging-24.2.dist-info/REQUESTED
setuptools/_vendor/packaging-24.2.dist-info/WHEEL
setuptools/_vendor/packaging/licenses/__init__.py
setuptools/_vendor/packaging/licenses/_spdx.py
setuptools/_vendor/platformdirs/__init__.py
setuptools/_vendor/platformdirs/__main__.py
setuptools/_vendor/platformdirs/android.py
setuptools/_vendor/platformdirs/api.py
setuptools/_vendor/platformdirs/macos.py
setuptools/_vendor/platformdirs/py.typed
setuptools/_vendor/platformdirs/unix.py
setuptools/_vendor/platformdirs/version.py
setuptools/_vendor/platformdirs/windows.py
setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER
setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA
setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD
setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED
setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL
setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE
setuptools/_vendor/tomli/__init__.py
setuptools/_vendor/tomli/_parser.py
setuptools/_vendor/tomli/_re.py
setuptools/_vendor/tomli/_types.py
setuptools/_vendor/tomli/py.typed
setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER
setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE
setuptools/_vendor/tomli-2.0.1.dist-info/METADATA
setuptools/_vendor/tomli-2.0.1.dist-info/RECORD
setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED
setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL
setuptools/_vendor/typeguard/__init__.py
setuptools/_vendor/typeguard/_checkers.py
setuptools/_vendor/typeguard/_config.py
setuptools/_vendor/typeguard/_decorators.py
setuptools/_vendor/typeguard/_exceptions.py
setuptools/_vendor/typeguard/_functions.py
setuptools/_vendor/typeguard/_importhook.py
setuptools/_vendor/typeguard/_memo.py
setuptools/_vendor/typeguard/_pytest_plugin.py
setuptools/_vendor/typeguard/_suppression.py
setuptools/_vendor/typeguard/_transformer.py
setuptools/_vendor/typeguard/_union_transformer.py
setuptools/_vendor/typeguard/_utils.py
setuptools/_vendor/typeguard/py.typed
setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER
setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE
setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA
setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD
setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL
setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt
setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt
setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER
setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE
setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA
setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD
setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL
setuptools/_vendor/wheel/__init__.py
setuptools/_vendor/wheel/__main__.py
setuptools/_vendor/wheel/_bdist_wheel.py
setuptools/_vendor/wheel/_setuptools_logging.py
setuptools/_vendor/wheel/bdist_wheel.py
setuptools/_vendor/wheel/macosx_libfile.py
setuptools/_vendor/wheel/metadata.py
setuptools/_vendor/wheel/util.py
setuptools/_vendor/wheel/wheelfile.py
setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER
setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt
setuptools/_vendor/wheel-0.45.1.dist-info/METADATA
setuptools/_vendor/wheel-0.45.1.dist-info/RECORD
setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED
setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL
setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt
setuptools/_vendor/wheel/cli/__init__.py
setuptools/_vendor/wheel/cli/convert.py
setuptools/_vendor/wheel/cli/pack.py
setuptools/_vendor/wheel/cli/tags.py
setuptools/_vendor/wheel/cli/unpack.py
setuptools/_vendor/wheel/vendored/__init__.py
setuptools/_vendor/wheel/vendored/vendor.txt
setuptools/_vendor/wheel/vendored/packaging/LICENSE
setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE
setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD
setuptools/_vendor/wheel/vendored/packaging/__init__.py
setuptools/_vendor/wheel/vendored/packaging/_elffile.py
setuptools/_vendor/wheel/vendored/packaging/_manylinux.py
setuptools/_vendor/wheel/vendored/packaging/_musllinux.py
setuptools/_vendor/wheel/vendored/packaging/_parser.py
setuptools/_vendor/wheel/vendored/packaging/_structures.py
setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py
setuptools/_vendor/wheel/vendored/packaging/markers.py
setuptools/_vendor/wheel/vendored/packaging/requirements.py
setuptools/_vendor/wheel/vendored/packaging/specifiers.py
setuptools/_vendor/wheel/vendored/packaging/tags.py
setuptools/_vendor/wheel/vendored/packaging/utils.py
setuptools/_vendor/wheel/vendored/packaging/version.py
setuptools/_vendor/zipp/__init__.py
setuptools/_vendor/zipp/glob.py
setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER
setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE
setuptools/_vendor/zipp-3.19.2.dist-info/METADATA
setuptools/_vendor/zipp-3.19.2.dist-info/RECORD
setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED
setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL
setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt
setuptools/_vendor/zipp/compat/__init__.py
setuptools/_vendor/zipp/compat/py310.py
setuptools/command/__init__.py
setuptools/command/_requirestxt.py
setuptools/command/alias.py
setuptools/command/bdist_egg.py
setuptools/command/bdist_rpm.py
setuptools/command/bdist_wheel.py
setuptools/command/build.py
setuptools/command/build_clib.py
setuptools/command/build_ext.py
setuptools/command/build_py.py
setuptools/command/develop.py
setuptools/command/dist_info.py
setuptools/command/easy_install.py
setuptools/command/editable_wheel.py
setuptools/command/egg_info.py
setuptools/command/install.py
setuptools/command/install_egg_info.py
setuptools/command/install_lib.py
setuptools/command/install_scripts.py
setuptools/command/launcher manifest.xml
setuptools/command/rotate.py
setuptools/command/saveopts.py
setuptools/command/sdist.py
setuptools/command/setopt.py
setuptools/command/test.py
setuptools/compat/__init__.py
setuptools/compat/py310.py
setuptools/compat/py311.py
setuptools/compat/py312.py
setuptools/compat/py39.py
setuptools/config/NOTICE
setuptools/config/__init__.py
setuptools/config/_apply_pyprojecttoml.py
setuptools/config/distutils.schema.json
setuptools/config/expand.py
setuptools/config/pyprojecttoml.py
setuptools/config/setupcfg.py
setuptools/config/setuptools.schema.json
setuptools/config/_validate_pyproject/NOTICE
setuptools/config/_validate_pyproject/__init__.py
setuptools/config/_validate_pyproject/error_reporting.py
setuptools/config/_validate_pyproject/extra_validations.py
setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py
setuptools/config/_validate_pyproject/fastjsonschema_validations.py
setuptools/config/_validate_pyproject/formats.py
setuptools/tests/__init__.py
setuptools/tests/contexts.py
setuptools/tests/environment.py
setuptools/tests/fixtures.py
setuptools/tests/mod_with_constant.py
setuptools/tests/namespaces.py
setuptools/tests/script-with-bom.py
setuptools/tests/server.py
setuptools/tests/test_archive_util.py
setuptools/tests/test_bdist_deprecations.py
setuptools/tests/test_bdist_egg.py
setuptools/tests/test_bdist_wheel.py
setuptools/tests/test_build.py
setuptools/tests/test_build_clib.py
setuptools/tests/test_build_ext.py
setuptools/tests/test_build_meta.py
setuptools/tests/test_build_py.py
setuptools/tests/test_config_discovery.py
setuptools/tests/test_core_metadata.py
setuptools/tests/test_depends.py
setuptools/tests/test_develop.py
setuptools/tests/test_dist.py
setuptools/tests/test_dist_info.py
setuptools/tests/test_distutils_adoption.py
setuptools/tests/test_easy_install.py
setuptools/tests/test_editable_install.py
setuptools/tests/test_egg_info.py
setuptools/tests/test_extern.py
setuptools/tests/test_find_packages.py
setuptools/tests/test_find_py_modules.py
setuptools/tests/test_glob.py
setuptools/tests/test_install_scripts.py
setuptools/tests/test_logging.py
setuptools/tests/test_manifest.py
setuptools/tests/test_namespaces.py
setuptools/tests/test_packageindex.py
setuptools/tests/test_sandbox.py
setuptools/tests/test_sdist.py
setuptools/tests/test_setopt.py
setuptools/tests/test_setuptools.py
setuptools/tests/test_shutil_wrapper.py
setuptools/tests/test_unicode_utils.py
setuptools/tests/test_virtualenv.py
setuptools/tests/test_warnings.py
setuptools/tests/test_wheel.py
setuptools/tests/test_windows_wrappers.py
setuptools/tests/text.py
setuptools/tests/textwrap.py
setuptools/tests/compat/__init__.py
setuptools/tests/compat/py39.py
setuptools/tests/config/__init__.py
setuptools/tests/config/setupcfg_examples.txt
setuptools/tests/config/test_apply_pyprojecttoml.py
setuptools/tests/config/test_expand.py
setuptools/tests/config/test_pyprojecttoml.py
setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py
setuptools/tests/config/test_setupcfg.py
setuptools/tests/config/downloads/__init__.py
setuptools/tests/config/downloads/preload.py
setuptools/tests/indexes/test_links_priority/external.html
setuptools/tests/indexes/test_links_priority/simple/foobar/index.html
setuptools/tests/integration/__init__.py
setuptools/tests/integration/helpers.py
setuptools/tests/integration/test_pip_install_sdist.py
tools/build_launchers.py
tools/finalize.py
tools/generate_validation_code.py
tools/vendored.py