!function(t){function s(t){var s={};for(var o in t)s[o]=t[o];return s}function o(t,s){t="undefined"!=typeof t?t:10,s="undefined"!=typeof s?s:"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";for(var o=s.charAt(Math.round(Math.random()*(s.length-11))),e=1;t>e;e++)o+=s.charAt(Math.round(Math.random()*(s.length-1)));return o}function e(s,o){var e=t.interpolate([s[0].valueOf(),s[1].valueOf()],[o[0].valueOf(),o[1].valueOf()]);return function(t){var s=e(t);return[new Date(s[0]),new Date(s[1])]}}function i(t){return"undefined"==typeof t}function r(t){return null==t||i(t)}function n(t,s){return t.length>0?t[s%t.length]:null}function a(){function s(s,n){var a=t.functor(o),p=t.functor(e),h=[],l=[],c=0,d=-1,u=0,f=!1;if(!n){n=["M"];for(var y=1;y<s.length;y++)n.push("L")}for(;++d<n.length;){for(u=c+r[n[d]],h=[];u>c;)i.call(this,s[c],c)?(h.push(a.call(this,s[c],c),p.call(this,s[c],c)),c++):(h=null,c=u);h?f&&h.length>0?(l.push("M",h[0],h[1]),f=!1):(l.push(n[d]),l=l.concat(h)):f=!0}return c!=s.length&&console.warn("Warning: not all vertices used in Path"),l.join(" ")}var o=function(t){return t[0]},e=function(t){return t[1]},i=function(){return!0},r={M:1,m:1,L:1,l:1,Q:2,q:2,T:1,t:1,S:2,s:2,C:3,c:3,Z:0,z:0};return s.x=function(t){return arguments.length?(o=t,s):o},s.y=function(t){return arguments.length?(e=t,s):e},s.defined=function(t){return arguments.length?(i=t,s):i},s.call=s,s}function p(){function t(t){return s.forEach(function(s){t=s(t)}),t}var s=Array.prototype.slice.call(arguments,0),o=s.length;return t.domain=function(o){return arguments.length?(s[0].domain(o),t):s[0].domain()},t.range=function(e){return arguments.length?(s[o-1].range(e),t):s[o-1].range()},t.step=function(t){return s[t]},t}function h(t,s){if(O.call(this,t,s),this.cssclass="mpld3-"+this.props.xy+"grid","x"==this.props.xy)this.transform="translate(0,"+this.ax.height+")",this.position="bottom",this.scale=this.ax.xdom,this.tickSize=-this.ax.height;else{if("y"!=this.props.xy)throw"unrecognized grid xy specifier: should be 'x' or 'y'";this.transform="translate(0,0)",this.position="left",this.scale=this.ax.ydom,this.tickSize=-this.ax.width}}function l(t,s){O.call(this,t,s);var o={bottom:[0,this.ax.height],top:[0,0],left:[0,0],right:[this.ax.width,0]},e={bottom:"x",top:"x",left:"y",right:"y"};this.transform="translate("+o[this.props.position]+")",this.props.xy=e[this.props.position],this.cssclass="mpld3-"+this.props.xy+"axis",this.scale=this.ax[this.props.xy+"dom"]}function c(t,s){if("undefined"==typeof s){if(this.ax=null,this.fig=null,"display"!==this.trans)throw"ax must be defined if transform != 'display'"}else this.ax=s,this.fig=s.fig;if(this.zoomable="data"===t,this.x=this["x_"+t],this.y=this["y_"+t],"undefined"==typeof this.x||"undefined"==typeof this.y)throw"unrecognized coordinate code: "+t}function d(t,s){O.call(this,t,s),this.data=t.fig.get_data(this.props.data),this.pathcodes=this.props.pathcodes,this.pathcoords=new c(this.props.coordinates,this.ax),this.offsetcoords=new c(this.props.offsetcoordinates,this.ax),this.datafunc=a()}function u(t,s){O.call(this,t,s),(null==this.props.facecolors||0==this.props.facecolors.length)&&(this.props.facecolors=["none"]),(null==this.props.edgecolors||0==this.props.edgecolors.length)&&(this.props.edgecolors=["none"]);var o=this.ax.fig.get_data(this.props.offsets);(null===o||0===o.length)&&(o=[null]);var e=Math.max(this.props.paths.length,o.length);if(o.length===e)this.offsets=o;else{this.offsets=[];for(var i=0;e>i;i++)this.offsets.push(n(o,i))}this.pathcoords=new c(this.props.pathcoordinates,this.ax),this.offsetcoords=new c(this.props.offsetcoordinates,this.ax)}function f(s,o){O.call(this,s,o);var e=this.props;e.facecolor="none",e.edgecolor=e.color,delete e.color,e.edgewidth=e.linewidth,delete e.linewidth,this.defaultProps=d.prototype.defaultProps,d.call(this,s,e),this.datafunc=t.svg.line().interpolate("linear")}function y(s,o){O.call(this,s,o),this.marker=null!==this.props.markerpath?0==this.props.markerpath[0].length?null:F.path().call(this.props.markerpath[0],this.props.markerpath[1]):null===this.props.markername?null:t.svg.symbol(this.props.markername).size(Math.pow(this.props.markersize,2))();var e={paths:[this.props.markerpath],offsets:s.fig.get_data(this.props.data),xindex:this.props.xindex,yindex:this.props.yindex,offsetcoordinates:this.props.coordinates,edgecolors:[this.props.edgecolor],edgewidths:[this.props.edgewidth],facecolors:[this.props.facecolor],alphas:[this.props.alpha],zorder:this.props.zorder,id:this.props.id};this.requiredProps=u.prototype.requiredProps,this.defaultProps=u.prototype.defaultProps,u.call(this,s,e)}function g(t,s){O.call(this,t,s),this.coords=new c(this.props.coordinates,this.ax)}function m(t,s){O.call(this,t,s),this.text=this.props.text,this.position=this.props.position,this.coords=new c(this.props.coordinates,this.ax)}function x(s,o){function e(t){return new Date(t[0],t[1],t[2],t[3],t[4],t[5])}function i(t,s){return"date"!==t?s:[e(s[0]),e(s[1])]}function r(s,o,e){var i="date"===s?t.time.scale():"log"===s?t.scale.log():t.scale.linear();return i.domain(o).range(e)}O.call(this,s,o),this.axnum=this.fig.axes.length,this.axid=this.fig.figid+"_ax"+(this.axnum+1),this.clipid=this.axid+"_clip",this.props.xdomain=this.props.xdomain||this.props.xlim,this.props.ydomain=this.props.ydomain||this.props.ylim,this.sharex=[],this.sharey=[],this.elements=[];var n=this.props.bbox;this.position=[n[0]*this.fig.width,(1-n[1]-n[3])*this.fig.height],this.width=n[2]*this.fig.width,this.height=n[3]*this.fig.height,this.props.xdomain=i(this.props.xscale,this.props.xdomain),this.props.ydomain=i(this.props.yscale,this.props.ydomain),this.x=this.xdom=r(this.props.xscale,this.props.xdomain,[0,this.width]),this.y=this.ydom=r(this.props.yscale,this.props.ydomain,[this.height,0]),"date"===this.props.xscale&&(this.x=F.multiscale(t.scale.linear().domain(this.props.xlim).range(this.props.xdomain.map(Number)),this.xdom)),"date"===this.props.yscale&&(this.x=F.multiscale(t.scale.linear().domain(this.props.ylim).range(this.props.ydomain.map(Number)),this.ydom));for(var a=this.props.axes,p=0;p<a.length;p++){var h=new F.Axis(this,a[p]);this.elements.push(h),(this.props.gridOn||h.props.grid.gridOn)&&this.elements.push(h.getGrid())}for(var l=this.props.paths,p=0;p<l.length;p++)this.elements.push(new F.Path(this,l[p]));for(var c=this.props.lines,p=0;p<c.length;p++)this.elements.push(new F.Line(this,c[p]));for(var d=this.props.markers,p=0;p<d.length;p++)this.elements.push(new F.Markers(this,d[p]));for(var u=this.props.texts,p=0;p<u.length;p++)this.elements.push(new F.Text(this,u[p]));for(var f=this.props.collections,p=0;p<f.length;p++)this.elements.push(new F.PathCollection(this,f[p]));for(var y=this.props.images,p=0;p<y.length;p++)this.elements.push(new F.Image(this,y[p]));this.elements.sort(function(t,s){return t.props.zorder-s.props.zorder})}function b(t,s){O.call(this,t,s),this.buttons=[],this.props.buttons.forEach(this.addButton.bind(this))}function v(t,s){O.call(this,t),this.toolbar=t,this.fig=this.toolbar.fig,this.cssclass="mpld3-"+s+"button",this.active=!1}function A(t,s){O.call(this,t,s)}function z(t,s){A.call(this,t,s);var o=F.ButtonFactory({buttonID:"reset",sticky:!1,onActivate:function(){this.toolbar.fig.reset()},icon:function(){return F.icons.reset}});this.fig.buttons.push(o)}function w(t,s){A.call(this,t,s),null===this.props.enabled&&(this.props.enabled=!this.props.button);var o=this.props.enabled;if(this.props.button){var e=F.ButtonFactory({buttonID:"zoom",sticky:!0,actions:["scroll","drag"],onActivate:this.activate.bind(this),onDeactivate:this.deactivate.bind(this),onDraw:function(){this.setState(o)},icon:function(){return F.icons.move}});this.fig.buttons.push(e)}}function _(t,s){A.call(this,t,s),null===this.props.enabled&&(this.props.enabled=!this.props.button);var o=this.props.enabled;if(this.props.button){var e=F.ButtonFactory({buttonID:"boxzoom",sticky:!0,actions:["drag"],onActivate:this.activate.bind(this),onDeactivate:this.deactivate.bind(this),onDraw:function(){this.setState(o)},icon:function(){return F.icons.zoom}});this.fig.buttons.push(e)}this.extentClass="boxzoombrush"}function k(t,s){A.call(this,t,s)}function P(t,s){F.Plugin.call(this,t,s),null===this.props.enabled&&(this.props.enabled=!this.props.button);var o=this.props.enabled;if(this.props.button){var e=F.ButtonFactory({buttonID:"linkedbrush",sticky:!0,actions:["drag"],onActivate:this.activate.bind(this),onDeactivate:this.deactivate.bind(this),onDraw:function(){this.setState(o)},icon:function(){return F.icons.brush}});this.fig.buttons.push(e)}this.extentClass="linkedbrush"}function B(t,s){F.Plugin.call(this,t,s)}function E(s,o){O.call(this,null,o),this.figid=s,this.width=this.props.width,this.height=this.props.height,this.data=this.props.data,this.buttons=[],this.root=t.select("#"+s).append("div").style("position","relative"),this.axes=[];for(var e=0;e<this.props.axes.length;e++)this.axes.push(new x(this,this.props.axes[e]));this.plugins=[];for(var e=0;e<this.props.plugins.length;e++)this.add_plugin(this.props.plugins[e]);this.toolbar=new F.Toolbar(this,{buttons:this.buttons})}function O(t,s){this.parent=r(t)?null:t,this.props=r(s)?{}:this.processProps(s),this.fig=t instanceof E?t:t&&"fig"in t?t.fig:null,this.ax=t instanceof x?t:t&&"ax"in t?t.ax:null}var F={_mpld3IsLoaded:!0,figures:[],plugin_map:{}};F.version="0.2",F.register_plugin=function(t,s){F.plugin_map[t]=s},F.draw_figure=function(t,s){var o=document.getElementById(t);if(null===o)throw t+" is not a valid id";var e=new F.Figure(t,s);return F.figures.push(e),e.draw(),e},F.cloneObj=s,F.merge_objects=function(){for(var t,s={},o=0;o<arguments.length;o++){t=arguments[o];for(var e in t)s[e]=t[e]}return s},F.generate_id=function(t,s){return console.warn("mpld3.generate_id is deprecated. Use mpld3.generateId instead."),o(t,s)},F.generateId=o,F.get_element=function(t,s){var o,e,i;o="undefined"==typeof s?F.figures:"undefined"==typeof s.length?[s]:s;for(var r=0;r<o.length;r++){if(s=o[r],s.props.id===t)return s;for(var n=0;n<s.axes.length;n++){if(e=s.axes[n],e.props.id===t)return e;for(var a=0;a<e.elements.length;a++)if(i=e.elements[a],i.props.id===t)return i}}return null},F.insert_css=function(t,s){var o=document.head||document.getElementsByTagName("head")[0],e=document.createElement("style"),i=t+" {";for(var r in s)i+=r+":"+s[r]+"; ";i+="}",e.type="text/css",e.styleSheet?e.styleSheet.cssText=i:e.appendChild(document.createTextNode(i)),o.appendChild(e)},F.process_props=function(t,s,o,e){function i(t){O.call(this,null,t)}console.warn("mpld3.process_props is deprecated. Plot elements should derive from mpld3.PlotElement"),i.prototype=Object.create(O.prototype),i.prototype.constructor=i,i.prototype.requiredProps=e,i.prototype.defaultProps=o;var r=new i(s);return r.props},F.interpolateDates=e,F.path=function(){return a()},F.multiscale=p,F.icons={reset:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABmJLR0QA/wD/AP+gvaeTAAAACXBI\nWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3gIcACMoD/OzIwAAAJhJREFUOMtjYKAx4KDUgNsMDAx7\nyNV8i4GB4T8U76VEM8mGYNNMtCH4NBM0hBjNMIwSsMzQ0MamcDkDA8NmQi6xggpUoikwQbIkHk2u\nE0rLI7vCBknBSyxeRDZAE6qHgQkq+ZeBgYERSfFPAoHNDNUDN4BswIRmKgxwEasP2dlsDAwMYlA/\n/mVgYHiBpkkGKscIDaPfVMmuAGnOTaGsXF0MAAAAAElFTkSuQmCC\n",move:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABmJLR0QA/wD/AP+gvaeTAAAACXBI\nWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3gIcACQMfLHBNQAAANZJREFUOMud07FKA0EQBuAviaKB\nlFr7COJrpAyYRlKn8hECEkFEn8ROCCm0sBMRYgh5EgVFtEhsRjiO27vkBoZd/vn5d3b+XcrjFI9q\nxgXWkc8pUjOB93GMd3zgB9d1unjDSxmhWSHQqOJki+MtOuv/b3ZifUqctIrMxwhHuG1gim4Ma5kR\nWuEkXFgU4B0MW1Ho4TeyjX3s4TDq3zn8ALvZ7q5wX9DqLOHCDA95cFBAnOO1AL/ZdNopgY3fQcqF\nyriMe37hM9w521ZkkvlMo7o/8g7nZYQ/QDctp1nTCf0AAAAASUVORK5CYII=\n",zoom:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABmJLR0QA/wD/AP+gvaeTAAAACXBI\nWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3gMPDiIRPL/2oQAAANBJREFUOMvF0b9KgzEcheHHVnCT\nKoI4uXbtLXgB3oJDJxevw1VwkoJ/NjepQ2/BrZRCx0ILFURQKV2kyOeSQpAmn7WDB0Lg955zEhLy\n2scdXlBggits+4WOQqjAJ3qYR7NGLrwXGU9+sGbEtlIF18FwmuBngZ+nCt6CIacC3Rx8LSl4xzgF\nn0tusBn4UyVhuA/7ZYIv5g+pE3ail25hN/qdmzCfpsJVjKKCZesDBwtzrAqGOMQj6vhCDRsY4ALH\nmOVObltR/xeG/jph6OD2r+Fv5lZBWEhMx58AAAAASUVORK5CYII=\n",brush:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABmJLR0QA/wD/AP+gvaeTAAAACXBI\nWXMAAEQkAABEJAFAZ8RUAAAAB3RJTUUH3gMCEiQKB9YaAgAAAWtJREFUOMuN0r1qVVEQhuFn700k\nnfEvBq0iNiIiOKXgH4KCaBeIhWARK/EibLwFCwVLjyAWaQzRGG9grC3URkHUBKKgRuWohWvL5pjj\nyTSLxcz7rZlZHyMiItqzFxGTEVF18/UoODNFxDIO4x12dkXqTcBPsCUzD+AK3ndFqhHwEsYz82gn\nN4dbmMRK9R/4KY7jAvbiWmYeHBT5Z4QCP8J1rGAeN3GvU3Mbl/Gq3qCDcxjLzOV+v78fq/iFIxFx\nPyJ2lNJpfBy2g59YzMyzEbEVLzGBJjOriLiBq5gaJrCIU3hcRCbwAtuwjm/Yg/V6I9NgDA1OR8RC\nZq6Vcd7iUwtn5h8fdMBdETGPE+Xe4ExELDRNs4bX2NfCUHe+7UExyfkCP8MhzOA7PuAkvrbwXyNF\nxF3MDqxiqlhXC7SPdaOKiN14g0u4g3H0MvOiTUSNY3iemb0ywmfMdfYyUmAJ2yPiBx6Wr/oy2Oqw\n+A1SupBzAOuE/AAAAABJRU5ErkJggg==\n"},F.Grid=h,h.prototype=Object.create(O.prototype),h.prototype.constructor=h,h.prototype.requiredProps=["xy"],h.prototype.defaultProps={color:"gray",dasharray:"2,2",alpha:"0.5",nticks:10,gridOn:!0,tickvalues:null,zorder:0},h.prototype.draw=function(){this.grid=t.svg.axis().scale(this.scale).orient(this.position).ticks(this.props.nticks).tickValues(this.props.tickvalues).tickSize(this.tickSize,0,0).tickFormat(""),this.elem=this.ax.axes.append("g").attr("class",this.cssclass).attr("transform",this.transform).call(this.grid),F.insert_css("div#"+this.ax.fig.figid+" ."+this.cssclass+" .tick",{stroke:this.props.color,"stroke-dasharray":this.props.dasharray,"stroke-opacity":this.props.alpha}),F.insert_css("div#"+this.ax.fig.figid+" ."+this.cssclass+" path",{"stroke-width":0})},h.prototype.zoomed=function(){this.elem.call(this.grid)},F.Axis=l,l.prototype=Object.create(O.prototype),l.prototype.constructor=l,l.prototype.requiredProps=["position"],l.prototype.defaultProps={nticks:10,tickvalues:null,tickformat:null,fontsize:"11px",fontcolor:"black",axiscolor:"black",scale:"linear",grid:{},zorder:0},l.prototype.getGrid=function(){var t={nticks:this.props.nticks,zorder:this.props.zorder,tickvalues:this.props.tickvalues,xy:this.props.xy};if(this.props.grid)for(var s in this.props.grid)t[s]=this.props.grid[s];return new h(this.ax,t)},l.prototype.draw=function(){this.axis=t.svg.axis().scale(this.scale).orient(this.props.position).ticks(this.props.nticks).tickValues(this.props.tickvalues).tickFormat(this.props.tickformat),this.elem=this.ax.baseaxes.append("g").attr("transform",this.transform).attr("class",this.cssclass).call(this.axis),F.insert_css("div#"+this.ax.fig.figid+" ."+this.cssclass+" line,  ."+this.cssclass+" path",{"shape-rendering":"crispEdges",stroke:this.props.axiscolor,fill:"none"}),F.insert_css("div#"+this.ax.fig.figid+" ."+this.cssclass+" text",{"font-family":"sans-serif","font-size":this.props.fontsize,fill:this.props.fontcolor,stroke:"none"})},l.prototype.zoomed=function(){this.elem.call(this.axis)},F.Coordinates=c,c.prototype.xy=function(t,s,o){return s="undefined"==typeof s?0:s,o="undefined"==typeof o?1:o,[this.x(t[s]),this.y(t[o])]},c.prototype.x_data=function(t){return this.ax.x(t)},c.prototype.y_data=function(t){return this.ax.y(t)},c.prototype.x_display=function(t){return t},c.prototype.y_display=function(t){return t},c.prototype.x_axes=function(t){return t*this.ax.width},c.prototype.y_axes=function(t){return this.ax.height*(1-t)},c.prototype.x_figure=function(t){return t*this.fig.width-this.ax.position[0]},c.prototype.y_figure=function(t){return(1-t)*this.fig.height-this.ax.position[1]},F.Path=d,d.prototype=Object.create(O.prototype),d.prototype.constructor=d,d.prototype.requiredProps=["data"],d.prototype.defaultProps={xindex:0,yindex:1,coordinates:"data",facecolor:"green",edgecolor:"black",edgewidth:1,dasharray:"10,0",pathcodes:null,offset:null,offsetcoordinates:"data",alpha:1,zorder:1},d.prototype.nanFilter=function(t){return!isNaN(t[this.props.xindex])&&!isNaN(t[this.props.yindex])},d.prototype.draw=function(){if(this.datafunc.defined(this.nanFilter.bind(this)).x(function(t){return this.pathcoords.x(t[this.props.xindex])}).y(function(t){return this.pathcoords.y(t[this.props.yindex])}),this.path=this.ax.axes.append("svg:path").attr("d",this.datafunc(this.data,this.pathcodes)).attr("class","mpld3-path").style("stroke",this.props.edgecolor).style("stroke-width",this.props.edgewidth).style("stroke-dasharray",this.props.dasharray).style("stroke-opacity",this.props.alpha).style("fill",this.props.facecolor).style("fill-opacity",this.props.alpha).attr("vector-effect","non-scaling-stroke"),null!==this.props.offset){var t=this.offsetcoords.xy(this.props.offset);this.path.attr("transform","translate("+t+")")}},d.prototype.elements=function(){return this.path},d.prototype.zoomed=function(){if(this.pathcoords.zoomable&&this.path.attr("d",this.datafunc(this.data,this.pathcodes)),null!==this.props.offset&&this.offsetcoords.zoomable){var t=this.offsetcoords.xy(this.props.offset);this.path.attr("transform","translate("+t+")")}},F.PathCollection=u,u.prototype=Object.create(O.prototype),u.prototype.constructor=u,u.prototype.requiredProps=["paths","offsets"],u.prototype.defaultProps={xindex:0,yindex:1,pathtransforms:[],pathcoordinates:"display",offsetcoordinates:"data",offsetorder:"before",edgecolors:["#000000"],edgewidths:[1],facecolors:["#0000FF"],alphas:[1],zorder:2},u.prototype.transformFunc=function(s,o){var e=this.props.pathtransforms,i=0==e.length?"":t.transform("matrix("+n(e,o)+")").toString(),r=null===s||"undefined"==typeof s?"translate(0, 0)":"translate("+this.offsetcoords.xy(s,this.props.xindex,this.props.yindex)+")";return"after"===this.props.offsetorder?i+r:r+i},u.prototype.pathFunc=function(t,s){return a().x(function(t){return this.pathcoords.x(t[0])}.bind(this)).y(function(t){return this.pathcoords.y(t[1])}.bind(this)).apply(this,n(this.props.paths,s))},u.prototype.styleFunc=function(t,s){var o={stroke:n(this.props.edgecolors,s),"stroke-width":n(this.props.edgewidths,s),"stroke-opacity":n(this.props.alphas,s),fill:n(this.props.facecolors,s),"fill-opacity":n(this.props.alphas,s)},e="";for(var i in o)e+=i+":"+o[i]+";";return e},u.prototype.draw=function(){this.group=this.ax.axes.append("svg:g"),this.pathsobj=this.group.selectAll("paths").data(this.offsets).enter().append("svg:path").attr("d",this.pathFunc.bind(this)).attr("class","mpld3-path").attr("transform",this.transformFunc.bind(this)).attr("style",this.styleFunc.bind(this)).attr("vector-effect","non-scaling-stroke")},u.prototype.elements=function(){return this.group.selectAll("path")},u.prototype.zoomed=function(){"data"===this.props.pathcoordinates&&this.pathsobj.attr("d",this.pathFunc.bind(this)),"data"===this.props.offsetcoordinates&&this.pathsobj.attr("transform",this.transformFunc.bind(this))},F.Line=f,f.prototype=Object.create(d.prototype),f.prototype.constructor=f,f.prototype.requiredProps=["data"],f.prototype.defaultProps={xindex:0,yindex:1,coordinates:"data",color:"salmon",linewidth:2,dasharray:"10,0",alpha:1,zorder:2},F.Markers=y,y.prototype=Object.create(u.prototype),y.prototype.constructor=y,y.prototype.requiredProps=["data"],y.prototype.defaultProps={xindex:0,yindex:1,coordinates:"data",facecolor:"salmon",edgecolor:"black",edgewidth:1,alpha:1,markersize:6,markername:"circle",markerpath:null,zorder:3},y.prototype.pathFunc=function(){return this.marker},F.Image=g,g.prototype=Object.create(O.prototype),g.prototype.constructor=g,g.prototype.requiredProps=["data","extent"],g.prototype.defaultProps={alpha:1,coordinates:"data",zorder:1},g.prototype.draw=function(){this.image=this.ax.axes.append("svg:image").attr("class","mpld3-image").attr("xlink:href","data:image/png;base64,"+this.props.data).style({opacity:this.props.alpha}).attr("preserveAspectRatio","none"),this.zoomed()},g.prototype.elements=function(){return t.select(this.image)},g.prototype.zoomed=function(){var t=this.props.extent;this.image.attr("x",this.coords.x(t[0])).attr("y",this.coords.y(t[3])).attr("width",this.coords.x(t[1])-this.coords.x(t[0])).attr("height",this.coords.y(t[2])-this.coords.y(t[3]))},F.Text=m,m.prototype=Object.create(O.prototype),m.prototype.constructor=m,m.prototype.requiredProps=["text","position"],m.prototype.defaultProps={coordinates:"data",h_anchor:"start",v_baseline:"auto",rotation:0,fontsize:11,color:"black",alpha:1,zorder:3},m.prototype.draw=function(){this.obj="data"==this.props.coordinates?this.ax.axes.append("text"):this.ax.baseaxes.append("text"),this.obj.attr("class","mpld3-text").text(this.text).style("text-anchor",this.props.h_anchor).style("dominant-baseline",this.props.v_baseline).style("font-size",this.props.fontsize).style("fill",this.props.color).style("opacity",this.props.alpha),this.applyTransform()},m.prototype.elements=function(){return t.select(this.obj)},m.prototype.applyTransform=function(){var t=this.coords.xy(this.position);this.obj.attr("x",t[0]).attr("y",t[1]),this.props.rotation&&this.obj.attr("transform","rotate("+this.props.rotation+","+t+")")},m.prototype.zoomed=function(){this.coords.zoomable&&this.applyTransform()},F.Axes=x,x.prototype=Object.create(O.prototype),x.prototype.constructor=x,x.prototype.requiredProps=["xlim","ylim"],x.prototype.defaultProps={bbox:[.1,.1,.8,.8],axesbg:"#FFFFFF",axesbgalpha:1,gridOn:!1,xdomain:null,ydomain:null,xscale:"linear",yscale:"linear",zoomable:!0,axes:[{position:"left"},{position:"bottom"}],lines:[],paths:[],markers:[],texts:[],collections:[],sharex:[],sharey:[],images:[]},x.prototype.draw=function(){for(var s=0;s<this.props.sharex.length;s++)this.sharex.push(F.get_element(this.props.sharex[s]));for(var s=0;s<this.props.sharey.length;s++)this.sharey.push(F.get_element(this.props.sharey[s]));this.zoom=t.behavior.zoom(),this.zoom.last_t=this.zoom.translate(),this.zoom.last_s=this.zoom.scale(),this.zoom_x=t.behavior.zoom().x(this.xdom),this.zoom_y=t.behavior.zoom().y(this.ydom),this.baseaxes=this.fig.canvas.append("g").attr("transform","translate("+this.position[0]+","+this.position[1]+")").attr("width",this.width).attr("height",this.height).attr("class","mpld3-baseaxes"),this.clip=this.baseaxes.append("svg:clipPath").attr("id",this.clipid).append("svg:rect").attr("x",0).attr("y",0).attr("width",this.width).attr("height",this.height),this.axes=this.baseaxes.append("g").attr("class","mpld3-axes").attr("clip-path","url(#"+this.clipid+")"),this.axesbg=this.axes.append("svg:rect").attr("width",this.width).attr("height",this.height).attr("class","mpld3-axesbg").style("fill",this.props.axesbg).style("fill-opacity",this.props.axesbgalpha);for(var s=0;s<this.elements.length;s++)this.elements[s].draw()},x.prototype.enable_zoom=function(){this.props.zoomable&&(this.zoom.on("zoom",this.zoomed.bind(this,!0)),this.axes.call(this.zoom),this.axes.style("cursor","move"))},x.prototype.disable_zoom=function(){this.props.zoomable&&(this.zoom.on("zoom",null),this.axes.on(".zoom",null),this.axes.style("cursor",null))},x.prototype.zoomed=function(t){if(t="undefined"==typeof t?!0:t){var s=this.zoom.translate()[0]-this.zoom.last_t[0],o=this.zoom.translate()[1]-this.zoom.last_t[1],e=this.zoom.scale()/this.zoom.last_s;this.zoom_x.translate([this.zoom_x.translate()[0]+s,0]),this.zoom_x.scale(this.zoom_x.scale()*e),this.zoom_y.translate([0,this.zoom_y.translate()[1]+o]),this.zoom_y.scale(this.zoom_y.scale()*e),this.zoom.last_t=this.zoom.translate(),this.zoom.last_s=this.zoom.scale(),this.sharex.forEach(function(t){t.zoom_x.translate(this.zoom_x.translate()).scale(this.zoom_x.scale())}.bind(this)),this.sharey.forEach(function(t){t.zoom_y.translate(this.zoom_y.translate()).scale(this.zoom_y.scale())}.bind(this)),this.sharex.forEach(function(t){t.zoomed(!1)}),this.sharey.forEach(function(t){t.zoomed(!1)})}for(var i=0;i<this.elements.length;i++)this.elements[i].zoomed()},x.prototype.reset=function(t,s){this.set_axlim(this.props.xdomain,this.props.ydomain,t,s)},x.prototype.set_axlim=function(s,o,e,n){s=r(s)?this.xdom.domain():s,o=r(o)?this.ydom.domain():o,e=r(e)?750:e,n=i(n)?!0:n;var a="date"===this.props.xscale?F.interpolateDates(this.xdom.domain(),s):t.interpolate(this.xdom.domain(),s),p="date"===this.props.yscale?F.interpolateDates(this.ydom.domain(),o):t.interpolate(this.ydom.domain(),o),h=function(t){this.zoom_x.x(this.xdom.domain(a(t))),this.zoom_y.y(this.ydom.domain(p(t))),this.zoomed(!1)}.bind(this);t.select({}).transition().duration(e).tween("zoom",function(){return h}),n&&(this.sharex.forEach(function(t){t.set_axlim(s,null,e,!1)}),this.sharey.forEach(function(t){t.set_axlim(null,o,e,!1)})),this.zoom.scale(1).translate([0,0]),this.zoom.last_t=this.zoom.translate(),this.zoom.last_s=this.zoom.scale(),this.zoom_x.scale(1).translate([0,0]),this.zoom_y.scale(1).translate([0,0])},F.Toolbar=b,b.prototype=Object.create(O.prototype),b.prototype.constructor=b,b.prototype.defaultProps={buttons:["reset","move"]},b.prototype.addButton=function(t){this.buttons.push(new t(this))},b.prototype.draw=function(){function s(){this.buttonsobj.transition(750).attr("y",0)}function o(){this.buttonsobj.transition(750).delay(250).attr("y",16)}F.insert_css("div#"+this.fig.figid+" .mpld3-toolbar image",{cursor:"pointer",opacity:.2,display:"inline-block",margin:"0px"}),F.insert_css("div#"+this.fig.figid+" .mpld3-toolbar image.active",{opacity:.4}),F.insert_css("div#"+this.fig.figid+" .mpld3-toolbar image.pressed",{opacity:.6}),this.fig.canvas.on("mouseenter",s.bind(this)).on("mouseleave",o.bind(this)).on("touchenter",s.bind(this)).on("touchstart",s.bind(this)),this.toolbar=this.fig.canvas.append("svg:svg").attr("width",16*this.buttons.length).attr("height",16).attr("x",2).attr("y",this.fig.height-16-2).attr("class","mpld3-toolbar"),this.buttonsobj=this.toolbar.append("svg:g").selectAll("buttons").data(this.buttons).enter().append("svg:image").attr("class",function(t){return t.cssclass}).attr("xlink:href",function(t){return t.icon()}).attr("width",16).attr("height",16).attr("x",function(t,s){return 16*s}).attr("y",16).on("click",function(t){t.click()}).on("mouseenter",function(){t.select(this).classed({active:1})}).on("mouseleave",function(){t.select(this).classed({active:0})});for(var e=0;e<this.buttons.length;e++)this.buttons[e].onDraw()},b.prototype.deactivate_all=function(){this.buttons.forEach(function(t){t.deactivate()})},b.prototype.deactivate_by_action=function(t){function s(s){return-1!==t.indexOf(s)}t.length>0&&this.buttons.forEach(function(t){t.actions.filter(s).length>0&&t.deactivate()})},F.Button=v,v.prototype=Object.create(O.prototype),v.prototype.constructor=v,v.prototype.setState=function(t){t?this.activate():this.deactivate()},v.prototype.click=function(){this.active?this.deactivate():this.activate()},v.prototype.activate=function(){this.toolbar.deactivate_by_action(this.actions),this.onActivate(),this.active=!0,this.toolbar.toolbar.select("."+this.cssclass).classed({pressed:!0}),this.sticky||this.deactivate()},v.prototype.deactivate=function(){this.onDeactivate(),this.active=!1,this.toolbar.toolbar.select("."+this.cssclass).classed({pressed:!1})},v.prototype.sticky=!1,v.prototype.actions=[],v.prototype.icon=function(){return""},v.prototype.onActivate=function(){},v.prototype.onDeactivate=function(){},v.prototype.onDraw=function(){},F.ButtonFactory=function(t){function s(t){v.call(this,t,this.buttonID)}if("string"!=typeof t.buttonID)throw"ButtonFactory: buttonID must be present and be a string";s.prototype=Object.create(v.prototype),s.prototype.constructor=s;for(var o in t)s.prototype[o]=t[o];return s},F.Plugin=A,A.prototype=Object.create(O.prototype),A.prototype.constructor=A,A.prototype.requiredProps=[],A.prototype.defaultProps={},A.prototype.draw=function(){},F.ResetPlugin=z,F.register_plugin("reset",z),z.prototype=Object.create(A.prototype),z.prototype.constructor=z,z.prototype.requiredProps=[],z.prototype.defaultProps={},F.ZoomPlugin=w,F.register_plugin("zoom",w),w.prototype=Object.create(A.prototype),w.prototype.constructor=w,w.prototype.requiredProps=[],w.prototype.defaultProps={button:!0,enabled:null},w.prototype.activate=function(){this.fig.enable_zoom()},w.prototype.deactivate=function(){this.fig.disable_zoom()},w.prototype.draw=function(){this.props.enabled?this.fig.enable_zoom():this.fig.disable_zoom()},F.BoxZoomPlugin=_,F.register_plugin("boxzoom",_),_.prototype=Object.create(A.prototype),_.prototype.constructor=_,_.prototype.requiredProps=[],_.prototype.defaultProps={button:!0,enabled:null},_.prototype.activate=function(){this.enable&&this.enable()},_.prototype.deactivate=function(){this.disable&&this.disable()},_.prototype.draw=function(){function t(t){if(this.enabled){var o=s.extent();s.empty()||t.set_axlim([o[0][0],o[1][0]],[o[0][1],o[1][1]])}t.axes.call(s.clear())}F.insert_css("#"+this.fig.figid+" rect.extent."+this.extentClass,{fill:"#fff","fill-opacity":0,stroke:"#999"});var s=this.fig.getBrush();this.enable=function(){this.fig.showBrush(this.extentClass),s.on("brushend",t.bind(this)),this.enabled=!0},this.disable=function(){this.fig.hideBrush(this.extentClass),this.enabled=!1},this.toggle=function(){this.enabled?this.disable():this.enable()},this.disable()},F.TooltipPlugin=k,F.register_plugin("tooltip",k),k.prototype=Object.create(A.prototype),k.prototype.constructor=k,k.prototype.requiredProps=["id"],k.prototype.defaultProps={labels:null,hoffset:0,voffset:10,location:"mouse"},k.prototype.draw=function(){function s(t,s){this.tooltip.style("visibility","visible").text(null===r?"("+t+")":n(r,s))}function o(){if("mouse"===a){var s=t.mouse(this.fig.canvas.node());this.x=s[0]+this.props.hoffset,this.y=s[1]-this.props.voffset}this.tooltip.attr("x",this.x).attr("y",this.y)}function e(){this.tooltip.style("visibility","hidden")}var i=F.get_element(this.props.id,this.fig),r=this.props.labels,a=this.props.location;this.tooltip=this.fig.canvas.append("text").attr("class","mpld3-tooltip-text").attr("x",0).attr("y",0).text("").style("visibility","hidden"),"bottom left"==a||"top left"==a?(this.x=i.ax.position[0]+5+this.props.hoffset,this.tooltip.style("text-anchor","beginning")):"bottom right"==a||"top right"==a?(this.x=i.ax.position[0]+i.ax.width-5+this.props.hoffset,this.tooltip.style("text-anchor","end")):this.tooltip.style("text-anchor","middle"),"bottom left"==a||"bottom right"==a?this.y=i.ax.position[1]+i.ax.height-5+this.props.voffset:("top left"==a||"top right"==a)&&(this.y=i.ax.position[1]+5+this.props.voffset),i.elements().on("mouseover",s.bind(this)).on("mousemove",o.bind(this)).on("mouseout",e.bind(this))},F.LinkedBrushPlugin=P,F.register_plugin("linkedbrush",P),P.prototype=Object.create(F.Plugin.prototype),P.prototype.constructor=P,P.prototype.requiredProps=["id"],P.prototype.defaultProps={button:!0,enabled:null},P.prototype.activate=function(){this.enable&&this.enable()},P.prototype.deactivate=function(){this.disable&&this.disable()},P.prototype.draw=function(){function s(s){l!=this&&(t.select(l).call(p.clear()),l=this,p.x(s.xdom).y(s.ydom))}function o(t){var s=h[t.axnum];if(s.length>0){var o=s[0].props.xindex,e=s[0].props.yindex,i=p.extent();p.empty()?c.selectAll("path").classed("mpld3-hidden",!1):c.selectAll("path").classed("mpld3-hidden",function(t){return i[0][0]>t[o]||i[1][0]<t[o]||i[0][1]>t[e]||i[1][1]<t[e]})}}function e(){p.empty()&&c.selectAll("path").classed("mpld3-hidden",!1)}var i=F.get_element(this.props.id);if(null===i)throw"LinkedBrush: no object with id='"+this.props.id+"' was found";var r=this.fig;if(!("offsets"in i.props))throw"Plot object with id='"+this.props.id+"' is not a scatter plot";var n="offsets"in i.props?"offsets":"data";F.insert_css("#"+r.figid+" rect.extent."+this.extentClass,{fill:"#000","fill-opacity":.125,stroke:"#fff"}),F.insert_css("#"+r.figid+" path.mpld3-hidden",{stroke:"#ccc !important",fill:"#ccc !important"});var a="mpld3data-"+i.props[n],p=r.getBrush(),h=[];r.axes.forEach(function(t){var s=[];t.elements.forEach(function(t){t.props[n]===i.props[n]&&(t.group.classed(a,!0),s.push(t))}),h.push(s)});var l,c=r.canvas.selectAll("."+a);this.enable=function(){this.fig.showBrush(this.extentClass),p.on("brushstart",s).on("brush",o).on("brushend",e),this.enabled=!0
},this.disable=function(){t.select(l).call(p.clear()),this.fig.hideBrush(this.extentClass),this.enabled=!1},this.disable()},F.register_plugin("mouseposition",B),B.prototype=Object.create(F.Plugin.prototype),B.prototype.constructor=B,B.prototype.requiredProps=[],B.prototype.defaultProps={fontsize:12,fmt:".3g"},B.prototype.draw=function(){for(var s=this.fig,o=t.format(this.props.fmt),e=s.canvas.append("text").attr("class","mpld3-coordinates").style("text-anchor","end").style("font-size",this.props.fontsize).attr("x",this.fig.width-5).attr("y",this.fig.height-5),i=0;i<this.fig.axes.length;i++){var r=function(){var r=s.axes[i];return function(){var s=t.mouse(this),i=r.x.invert(s[0]),n=r.y.invert(s[1]);e.text("("+o(i)+", "+o(n)+")")}}();s.axes[i].baseaxes.on("mousemove",r).on("mouseout",function(){e.text("")})}},F.Figure=E,E.prototype=Object.create(O.prototype),E.prototype.constructor=E,E.prototype.requiredProps=["width","height"],E.prototype.defaultProps={data:{},axes:[],plugins:[{type:"reset"},{type:"zoom"},{type:"boxzoom"}]},E.prototype.getBrush=function(){if("undefined"==typeof this._brush){var s=t.svg.brush().x(t.scale.linear()).y(t.scale.linear());this.root.selectAll(".mpld3-axes").data(this.axes).call(s),this.axes.forEach(function(t){s.x(t.xdom).y(t.ydom),t.axes.call(s)}),this._brush=s,this.hideBrush()}return this._brush},E.prototype.showBrush=function(t){t="undefined"==typeof t?"":t;var s=this.getBrush();s.on("brushstart",function(t){s.x(t.xdom).y(t.ydom)}),this.canvas.selectAll("rect.background").style("cursor","crosshair").style("pointer-events",null),this.canvas.selectAll("rect.extent, rect.resize").style("display",null).classed(t,!0)},E.prototype.hideBrush=function(t){t="undefined"==typeof t?"":t;var s=this.getBrush();s.on("brushstart",null).on("brush",null).on("brushend",function(t){t.axes.call(s.clear())}),this.canvas.selectAll("rect.background").style("cursor",null).style("pointer-events","visible"),this.canvas.selectAll("rect.extent, rect.resize").style("display","none").classed(t,!1)},E.prototype.add_plugin=function(t){var o=t.type;if("undefined"==typeof o)return void console.warn("unspecified plugin type. Skipping this");if(t=s(t),delete t.type,o in F.plugin_map&&(o=F.plugin_map[o]),"function"!=typeof o)return void console.warn("Skipping unrecognized plugin: "+o);if(t.clear_toolbar&&(this.props.toolbar=[]),"buttons"in t)if("string"==typeof t.buttons)this.props.toolbar.push(t.buttons);else for(var e=0;e<t.buttons.length;e++)this.props.toolbar.push(t.buttons[e]);this.plugins.push(new o(this,t))},E.prototype.draw=function(){this.canvas=this.root.append("svg:svg").attr("class","mpld3-figure").attr("width",this.width).attr("height",this.height);for(var t=0;t<this.axes.length;t++)this.axes[t].draw();this.disable_zoom();for(var t=0;t<this.plugins.length;t++)this.plugins[t].draw();this.toolbar.draw()},E.prototype.reset=function(t){this.axes.forEach(function(s){s.reset(t,!1)})},E.prototype.enable_zoom=function(){for(var t=0;t<this.axes.length;t++)this.axes[t].enable_zoom();this.zoom_on=!0},E.prototype.disable_zoom=function(){for(var t=0;t<this.axes.length;t++)this.axes[t].disable_zoom();this.zoom_on=!1},E.prototype.toggle_zoom=function(){this.zoom_on?this.disable_zoom():this.enable_zoom()},E.prototype.get_data=function(t){return null===t||"undefined"==typeof t?null:"string"==typeof t?this.data[t]:t},F.PlotElement=O,O.prototype.requiredProps=[],O.prototype.defaultProps={},O.prototype.processProps=function(t){t=s(t);var o={},e=this.name();this.requiredProps.forEach(function(s){if(!(s in t))throw"property '"+s+"' must be specified for "+e;o[s]=t[s],delete t[s]});for(var i in this.defaultProps)i in t?(o[i]=t[i],delete t[i]):o[i]=this.defaultProps[i];"id"in t?(o.id=t.id,delete t.id):"id"in o||(o.id=F.generateId());for(var i in t)console.warn("Unrecognized property '"+i+"' for object "+this.name()+" (value = "+t[i]+").");return o},O.prototype.name=function(){var t=/function (.{1,})\(/,s=t.exec(this.constructor.toString());return s&&s.length>1?s[1]:""},"object"==typeof module&&module.exports?module.exports=F:this.mpld3=F,console.log("Loaded mpld3 version "+F.version)}(d3);